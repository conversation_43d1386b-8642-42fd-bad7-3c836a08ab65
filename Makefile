.PHONY: server dev setup setup-env-permanent clean-env show-env stop force-stop restart force-restart status clean-defunct help logs logs-view logs-clean logcat loglist logshow loghelp minio-start minio-stop minio-status minio-logs

# 默认目标：显示帮助
.DEFAULT_GOAL := help

# 启动所有服务器
server:
	PHX_SERVER=true iex -S mix phx.server

# 开发模式启动
dev:
	mix phx.server

daemon:
	@echo "🚀 正在后台启动 Phoenix 服务..."
	@mkdir -p logs
	@if pgrep -f "beam.*phx.server\|beam.*PHX_SERVER\|mix.*phx.server\|iex.*phx.server" > /dev/null; then \
		echo "⚠️  检测到已有 Phoenix 服务正在运行:"; \
		ps aux | grep -E "beam.*phx\.server|beam.*PHX_SERVER|mix.*phx\.server|iex.*phx\.server" | grep -v grep | \
		awk '{printf "  🔹 PID: %-8s CMD: %.60s\n", $$2, substr($$0, index($$0,$$11))}'; \
		echo ""; \
		echo "💡 如需重启服务，请先运行: make stop"; \
		echo "💡 如需强制重启，请运行: make force-restart"; \
		exit 1; \
	fi
	@echo "📝 日志将保存到: logs/phoenix_daemon.log"
	@echo "🔧 启动命令: nohup mix phx.server"
	@nohup mix phx.server > logs/phoenix_daemon.log 2>&1 & \
	DAEMON_PID=$$!; \
	echo "✅ Phoenix 服务已在后台启动"; \
	echo "📋 进程 PID: $$DAEMON_PID"; \
	echo "📁 日志文件: logs/phoenix_daemon.log"; \
	echo ""; \
	echo "🔍 等待服务启动..."; \
	sleep 3; \
	if kill -0 $$DAEMON_PID 2>/dev/null; then \
		echo "✅ 服务启动成功，正在运行中"; \
		echo ""; \
		echo "📊 快捷操作:"; \
		echo "  make status         # 查看服务状态"; \
		echo "  make logs-daemon    # 查看后台日志"; \
		echo "  make stop           # 停止服务"; \
		echo "  tail -f logs/phoenix_daemon.log  # 实时查看日志"; \
	else \
		echo "❌ 服务启动失败，请查看日志: logs/phoenix_daemon.log"; \
		exit 1; \
	fi
	
# 初始设置 - 包含永久环境变量导出
setup:
	@echo "🔧 运行 mix setup..."
	mix setup

# 重置数据库
reset:
	mix ecto.drop --force-drop
	@make setup

# 永久导出环境变量到系统环境
setup-env-permanent:
	@echo "🔧 正在配置永久环境变量..."
	@if [ ! -f .env ]; then \
		echo "⚠️  警告: .env 文件不存在，跳过环境变量配置"; \
		echo "💡 请创建 .env 文件并重新运行 make setup"; \
		exit 0; \
	fi
	@echo "📋 验证 .env 文件格式..."
	@if ! grep -q '^[A-Za-z_][A-Za-z0-9_]*=' .env 2>/dev/null; then \
		echo "⚠️  警告: .env 文件中未找到有效的环境变量格式 (KEY=VALUE)"; \
		echo "💡 请检查 .env 文件格式并重新运行"; \
		exit 0; \
	fi
	@echo "✅ .env 文件格式验证通过"
	@echo "🔧 正在导出环境变量到多个位置以确保持久性..."

	# 方法1: 导出到用户的 .bashrc (当前用户会话)
	@echo "📝 更新用户 .bashrc 文件..."
	@if [ -f ~/.bashrc ]; then \
		echo "# Cypridina Project Environment Variables - Auto-generated by Makefile" >> ~/.bashrc; \
		echo "# Generated on: $$(date)" >> ~/.bashrc; \
		grep -v '^#' .env | grep -v '^$$' | grep '=' | while IFS='=' read -r key value; do \
			if [ -n "$$key" ] && [ -n "$$value" ]; then \
				echo "export $$key=$$value" >> ~/.bashrc; \
			fi; \
		done; \
		echo "# End of Cypridina Environment Variables" >> ~/.bashrc; \
		echo "✅ 已更新 ~/.bashrc"; \
	else \
		echo "⚠️  ~/.bashrc 不存在，跳过用户级配置"; \
	fi

	# 方法2: 导出到用户的 .profile (POSIX兼容)
	@echo "📝 更新用户 .profile 文件..."
	@if [ -f ~/.profile ]; then \
		echo "# Cypridina Project Environment Variables - Auto-generated by Makefile" >> ~/.profile; \
		echo "# Generated on: $$(date)" >> ~/.profile; \
		grep -v '^#' .env | grep -v '^$$' | grep '=' | while IFS='=' read -r key value; do \
			if [ -n "$$key" ] && [ -n "$$value" ]; then \
				echo "export $$key=$$value" >> ~/.profile; \
			fi; \
		done; \
		echo "# End of Cypridina Environment Variables" >> ~/.profile; \
		echo "✅ 已更新 ~/.profile"; \
	else \
		echo "⚠️  ~/.profile 不存在，跳过用户级 POSIX 配置"; \
	fi

	# 方法3: 导出到系统级 /etc/environment (需要权限)
	@echo "📝 尝试更新系统级环境变量..."
	@if [ -w /etc/environment ] || [ "$$(id -u)" = "0" ]; then \
		echo "# Cypridina Project Environment Variables - Auto-generated by Makefile" >> /etc/environment; \
		echo "# Generated on: $$(date)" >> /etc/environment; \
		grep -v '^#' .env | grep -v '^$$' | grep '=' | while IFS='=' read -r key value; do \
			if [ -n "$$key" ] && [ -n "$$value" ]; then \
				echo "$$key=$$value" >> /etc/environment; \
			fi; \
		done; \
		echo "# End of Cypridina Environment Variables" >> /etc/environment; \
		echo "✅ 已更新 /etc/environment (系统级)"; \
	else \
		echo "⚠️  无权限写入 /etc/environment，跳过系统级配置"; \
		echo "💡 如需系统级配置，请使用 sudo make setup"; \
	fi



	@echo "🎯 环境变量配置完成！"
	@echo ""
	@echo "📋 配置摘要:"
	@echo "  ✅ 用户级配置: ~/.bashrc, ~/.profile"
	@if [ -w /etc/environment ] || [ "$$(id -u)" = "0" ]; then \
		echo "  ✅ 系统级配置: /etc/environment"; \
	else \
		echo "  ⚠️  系统级配置: 跳过 (权限不足)"; \
	fi
	@echo ""
	@echo "💡 使用说明:"
	@echo "  - 新终端会话将自动加载环境变量"
	@echo "  - 当前会话请运行: source ~/.bashrc"
	@echo "  - 重启系统后环境变量仍然有效"
	@echo "  - 如需清理环境变量: make clean-env"

# 清理之前导出的环境变量
clean-env:
	@echo "🧹 正在清理 Cypridina 项目的环境变量..."
	@echo "⚠️  这将从系统配置文件中移除 Cypridina 相关的环境变量"
	@read -p "确认继续？(y/N): " confirm; \
	if [ "$$confirm" != "y" ] && [ "$$confirm" != "Y" ]; then \
		echo "❌ 操作已取消"; \
		exit 0; \
	fi

	# 清理 ~/.bashrc
	@if [ -f ~/.bashrc ]; then \
		echo "🔧 清理 ~/.bashrc..."; \
		sed -i '/# Cypridina Project Environment Variables/,/# End of Cypridina Environment Variables/d' ~/.bashrc 2>/dev/null || true; \
		echo "✅ 已清理 ~/.bashrc"; \
	fi

	# 清理 ~/.profile
	@if [ -f ~/.profile ]; then \
		echo "🔧 清理 ~/.profile..."; \
		sed -i '/# Cypridina Project Environment Variables/,/# End of Cypridina Environment Variables/d' ~/.profile 2>/dev/null || true; \
		echo "✅ 已清理 ~/.profile"; \
	fi

	# 清理 /etc/environment (需要权限)
	@if [ -w /etc/environment ] || [ "$$(id -u)" = "0" ]; then \
		echo "🔧 清理 /etc/environment..."; \
		sed -i '/# Cypridina Project Environment Variables/,/# End of Cypridina Environment Variables/d' /etc/environment 2>/dev/null || true; \
		echo "✅ 已清理 /etc/environment"; \
	else \
		echo "⚠️  无权限清理 /etc/environment，请使用 sudo make clean-env"; \
	fi



	@echo "🎯 环境变量清理完成！"
	@echo "💡 请重启终端或运行 'source ~/.bashrc' 使更改生效"

# 显示当前环境变量状态
show-env:
	@echo "🔍 Cypridina 项目环境变量状态检查"
	@echo ""
	@echo "📋 .env 文件内容:"
	@if [ -f .env ]; then \
		echo "✅ .env 文件存在"; \
		echo "📄 变量列表:"; \
		grep -v '^#' .env | grep -v '^$$' | grep '=' | cut -d'=' -f1 | sed 's/^/  - /' || echo "  (无有效变量)"; \
	else \
		echo "❌ .env 文件不存在"; \
	fi
	@echo ""
	@echo "🔧 当前会话中的环境变量:"
	@if [ -f .env ]; then \
		grep -v '^#' .env | grep -v '^$$' | grep '=' | cut -d'=' -f1 | while read var; do \
			if [ -n "$${!var}" ]; then \
				echo "  ✅ $$var = $${!var}"; \
			else \
				echo "  ❌ $$var = (未设置)"; \
			fi; \
		done; \
	fi
	@echo ""
	@echo "📁 配置文件状态:"
	@if grep -q "Cypridina Project Environment Variables" ~/.bashrc 2>/dev/null; then \
		echo "  ✅ ~/.bashrc 包含 Cypridina 环境变量"; \
	else \
		echo "  ❌ ~/.bashrc 不包含 Cypridina 环境变量"; \
	fi
	@if grep -q "Cypridina Project Environment Variables" ~/.profile 2>/dev/null; then \
		echo "  ✅ ~/.profile 包含 Cypridina 环境变量"; \
	else \
		echo "  ❌ ~/.profile 不包含 Cypridina 环境变量"; \
	fi
	@if grep -q "Cypridina Project Environment Variables" /etc/environment 2>/dev/null; then \
		echo "  ✅ /etc/environment 包含 Cypridina 环境变量"; \
	else \
		echo "  ❌ /etc/environment 不包含 Cypridina 环境变量"; \
	fi


# 运行测试
test:
	mix test

# 停止Phoenix服务 (优雅停止)
stop:
	@echo "🛑 正在优雅停止 Phoenix 服务..."
	@FOUND_PROCESS=false; \
	if pgrep -f "beam.*phx.server\|beam.*PHX_SERVER\|mix.*phx.server\|iex.*phx.server" > /dev/null; then \
		echo "🔍 找到正在运行的 Phoenix 进程:"; \
		ps aux | grep -E "beam.*phx\.server|beam.*PHX_SERVER|mix.*phx\.server|iex.*phx\.server" | grep -v grep | \
		awk '{printf "  📋 PID: %-8s CMD: %.60s\n", $$2, substr($$0, index($$0,$$11))}'; \
		echo "✉️  发送 TERM 信号进行优雅停止..."; \
		pkill -TERM -f "beam.*phx.server\|beam.*PHX_SERVER\|mix.*phx.server\|iex.*phx.server"; \
		FOUND_PROCESS=true; \
	fi; \
	if [ "$$FOUND_PROCESS" = "true" ]; then \
		echo "⏳ 等待进程正常退出..."; \
		for i in 1 2 3 4 5; do \
			sleep 1; \
			if ! pgrep -f "beam.*phx.server\|beam.*PHX_SERVER\|mix.*phx.server\|iex.*phx.server" > /dev/null; then \
				echo "✅ Phoenix 服务已在 $$i 秒内成功停止"; \
				exit 0; \
			fi; \
			echo "⏱️  等待中... ($$i/5)"; \
		done; \
		echo "⚠️  进程在5秒内未完全退出，检查剩余进程:"; \
		if pgrep -f "beam.*phx.server\|beam.*PHX_SERVER\|mix.*phx.server\|iex.*phx.server" > /dev/null; then \
			ps aux | grep -E "beam.*phx\.server|beam.*PHX_SERVER|mix.*phx\.server|iex.*phx\.server" | grep -v grep | \
			awk '{printf "  ⚠️  PID: %-8s CMD: %.60s\n", $$2, substr($$0, index($$0,$$11))}'; \
			echo "💡 建议使用 'make force-stop' 进行强制停止"; \
		else \
			echo "✅ 所有进程已成功停止"; \
		fi; \
	else \
		echo "ℹ️  没有找到正在运行的 Phoenix 服务"; \
	fi

# 强制停止所有相关进程
force-stop:
	@echo "💥 正在强制停止所有相关进程..."
	@STOPPED_SOMETHING=false; \
	echo "🔍 查找并停止 Phoenix 相关进程..."; \
	if pgrep -f "beam.*phx.server\|beam.*PHX_SERVER\|mix.*phx.server\|iex.*phx.server" > /dev/null; then \
		echo "⚠️  发现 Phoenix 相关进程:"; \
		ps aux | grep -E "beam.*phx\.server|beam.*PHX_SERVER|mix.*phx\.server|iex.*phx\.server" | grep -v grep | \
		awk '{printf "  💀 PID: %-8s CMD: %.60s\n", $$2, substr($$0, index($$0,$$11))}'; \
		pkill -KILL -f "beam.*phx.server\|beam.*PHX_SERVER\|mix.*phx.server\|iex.*phx.server"; \
		echo "✅ 强制终止 Phoenix 相关进程"; \
		STOPPED_SOMETHING=true; \
	else \
		echo "ℹ️  没有找到 Phoenix 相关进程"; \
	fi; \
	echo "🔍 查找其他可疑的 beam/elixir 进程..."; \
	SUSPECT_PIDS=$$(ps aux | grep -E "beam.*cypridina|beam.*mix|iex.*cypridina|elixir.*cypridina" | grep -v grep | awk '{print $$2}'); \
	if [ -n "$$SUSPECT_PIDS" ]; then \
		echo "⚠️  发现可疑的 Elixir/Beam 进程:"; \
		ps aux | grep -E "beam.*cypridina|beam.*mix|iex.*cypridina|elixir.*cypridina" | grep -v grep | \
		awk '{printf "  💀 PID: %-8s CMD: %.60s\n", $$2, substr($$0, index($$0,$$11))}'; \
		echo "$$SUSPECT_PIDS" | xargs -r kill -KILL 2>/dev/null; \
		echo "✅ 强制终止可疑进程"; \
		STOPPED_SOMETHING=true; \
	else \
		echo "ℹ️  没有找到可疑的 Elixir/Beam 进程"; \
	fi; \
	echo "🧹 检查并清理端口占用..."; \
	PORTS="4000 4001 4002"; \
	for PORT in $$PORTS; do \
		if lsof -ti:$$PORT > /dev/null 2>&1; then \
			echo "🔧 发现端口 $$PORT 被占用，尝试释放:"; \
			lsof -i:$$PORT | tail -n +2 | awk '{printf "     💀 PID: %-8s 进程: %s\n", $$2, $$1}'; \
			lsof -ti:$$PORT | xargs kill -9 2>/dev/null; \
			echo "✅ 端口 $$PORT 已释放"; \
			STOPPED_SOMETHING=true; \
		fi; \
	done; \
	if [ "$$STOPPED_SOMETHING" = "false" ]; then \
		echo "ℹ️  没有发现需要停止的进程或端口占用"; \
	else \
		echo "🎯 等待系统清理..."; \
		sleep 1; \
		echo "🔍 验证清理结果:"; \
		if pgrep -f "beam.*phx.server\|beam.*PHX_SERVER\|mix.*phx.server\|iex.*phx.server" > /dev/null; then \
			echo "⚠️  仍有进程残留，可能需要重启系统"; \
		else \
			echo "✅ 所有相关进程已清理完毕"; \
		fi; \
	fi; \
	echo "✅ 强制停止操作完成"

# 重启服务 (优雅停止后启动)
restart:
	@echo "🔄 重启 Phoenix 服务..."
	@echo "第一步: 优雅停止现有服务"
	@$(MAKE) stop
	@echo ""
	@echo "第二步: 等待2秒确保完全停止"
	@sleep 2
	@echo ""
	@echo "第三步: 启动服务"
	@echo "🚀 启动 Phoenix 开发服务器..."
	@$(MAKE) dev

# 强制重启服务 (强制停止后启动)
force-restart:
	@echo "🔄 强制重启 Phoenix 服务..."
	@echo "第一步: 强制停止所有相关进程"
	@$(MAKE) force-stop
	@echo ""
	@echo "第二步: 等待3秒确保系统清理完毕"
	@sleep 3
	@echo ""
	@echo "第三步: 启动服务"
	@echo "🚀 启动 Phoenix 开发服务器..."
	@$(MAKE) dev

# 检查服务状态
status:
	@echo "🔍 检查 Phoenix 服务状态..."
	@echo ""
	@echo "📊 Phoenix 相关进程:"
	@if pgrep -f "beam.*phx.server\|beam.*PHX_SERVER\|mix.*phx.server\|iex.*phx.server" > /dev/null; then \
		ps aux | grep -E "beam.*phx\.server|beam.*PHX_SERVER|mix.*phx\.server|iex.*phx\.server" | grep -v grep | grep -v defunct | \
		awk '{printf "  🟢 PID: %-8s CPU: %-6s MEM: %-6s 运行时间: %-8s CMD: %.50s\n", $$2, $$3"%", $$4"%", $$10, substr($$0, index($$0,$$11))}'; \
		echo ""; \
		UPTIME=$$(ps -o etime= -p $$(pgrep -f "beam.*phx.server\|beam.*PHX_SERVER" | head -1) 2>/dev/null | tr -d ' '); \
		if [ -n "$$UPTIME" ]; then \
			echo "  ⏱️  主进程运行时间: $$UPTIME"; \
		fi; \
	else \
		echo "  ❌ 没有运行中的 Phoenix 进程"; \
	fi
	@echo ""
	@echo "🌐 端口使用情况:"
	@PORTS="4000 4001 4002"; \
	for PORT in $$PORTS; do \
		if lsof -ti:$$PORT > /dev/null 2>&1; then \
			echo "  🟢 端口 $$PORT: 被占用"; \
			lsof -i:$$PORT | tail -n +2 | awk '{printf "     📋 PID: %-8s 进程: %-12s 命令: %s\n", $$2, $$1, $$9}'; \
		else \
			echo "  ✅ 端口 $$PORT: 可用"; \
		fi; \
	done
	@echo ""
	@echo "💾 活跃的 beam/elixir 进程 (排除僵尸进程):"
	@ACTIVE_BEAMS=$$(ps aux | grep -E "beam|elixir" | grep -v grep | grep -v defunct | wc -l); \
	if [ $$ACTIVE_BEAMS -gt 0 ]; then \
		ps aux | grep -E "beam|elixir" | grep -v grep | grep -v defunct | head -10 | \
		awk '{printf "  🔹 PID: %-8s CPU: %-6s MEM: %-6s CMD: %.60s\n", $$2, $$3"%", $$4"%", substr($$0, index($$0,$$11))}'; \
		if [ $$ACTIVE_BEAMS -gt 10 ]; then \
			echo "  ... 还有 $$(($$ACTIVE_BEAMS - 10)) 个进程 (使用 'ps aux | grep beam' 查看全部)"; \
		fi; \
	else \
		echo "  ✅ 没有运行中的 beam/elixir 进程"; \
	fi
	@echo ""
	@DEFUNCT_COUNT=$$(ps aux | grep -E "beam|elixir" | grep defunct | wc -l); \
	if [ $$DEFUNCT_COUNT -gt 0 ]; then \
		echo "⚠️  发现 $$DEFUNCT_COUNT 个僵尸进程，建议使用 'make clean-defunct' 清理"; \
	fi
	@echo ""
	@echo "🎯 快捷操作:"
	@echo "  make stop           # 优雅停止服务"
	@echo "  make force-stop     # 强制停止服务" 
	@echo "  make restart        # 重启服务"
	@echo "  make clean-defunct  # 清理僵尸进程"

# 清理僵尸进程
clean-defunct:
	@echo "🧹 清理僵尸 beam 进程..."
	@DEFUNCT_COUNT=$$(ps aux | grep "beam" | grep defunct | wc -l); \
	if [ $$DEFUNCT_COUNT -gt 0 ]; then \
		echo "🔍 发现 $$DEFUNCT_COUNT 个僵尸进程"; \
		echo "ℹ️  僵尸进程通常会被系统自动清理，但我们可以尝试清理它们的父进程"; \
		echo "⚠️  注意：这可能会影响正在运行的进程"; \
		read -p "是否继续？(y/N): " confirm; \
		if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
			ps aux | grep "beam.*defunct" | awk '{print $$2}' | xargs -r kill -9 2>/dev/null || true; \
			echo "✅ 尝试清理完成"; \
		else \
			echo "❌ 操作已取消"; \
		fi; \
	else \
		echo "✅ 没有发现僵尸 beam 进程"; \
	fi

# 显示帮助信息
help:
	@echo "🚀 Cypridina 项目 Makefile 命令"
	@echo ""
	@echo "📋 可用命令:"
	@echo ""
	@echo "  🔧 开发和运行:"
	@echo "    make server         # 启动完整的 Phoenix 服务器 (自动加载.env环境变量)"
	@echo "    make dev            # 启动开发模式 Phoenix 服务器 (自动加载.env环境变量)"
	@echo ""
	@echo "  🛑 停止和重启服务:"
	@echo "    make stop           # 优雅停止 Phoenix 服务 (发送 TERM 信号)"
	@echo "    make force-stop     # 强制停止所有相关进程 (发送 KILL 信号)"
	@echo "    make restart        # 重启服务 (优雅停止后重新启动)"
	@echo "    make force-restart  # 强制重启服务 (强制停止后重新启动)"
	@echo "    make status         # 检查服务运行状态和资源使用情况"
	@echo "    make clean-defunct  # 清理僵尸进程"
	@echo ""
	@echo "  🔧 项目管理:"
	@echo "    make setup          # 初始项目设置 (依赖、数据库、永久环境变量)"
	@echo "    make reset          # 重置数据库"
	@echo "    make test           # 运行测试"
	@echo ""
	@echo "  🌍 环境变量管理:"
	@echo "    make setup-env-permanent  # 永久导出 .env 文件中的环境变量"
	@echo "    make clean-env      # 清理之前导出的环境变量"
	@echo "    make show-env       # 显示当前环境变量状态"
	@echo ""
	@echo "  🗄️ MinIO 服务管理:"
	@echo "    make minio-start    # 启动 MinIO 服务 (需要 Docker)"
	@echo "    make minio-stop     # 停止 MinIO 服务"
	@echo "    make minio-status   # 查看 MinIO 服务状态"
	@echo "    make minio-logs     # 查看 MinIO 服务日志"
	@echo ""
	@echo "  📝 日志管理:"
	@echo "    make logs           # 创建日志目录"
	@echo "    make logs-view      # 查看各种日志文件"
	@echo "    make logs-clean     # 清理日志文件"
	@echo "    make logcat 22      # 跟踪指定游戏的日志 (实时)"
	@echo "    make loglist        # 列出所有游戏日志"
	@echo "    make logshow 22     # 显示指定游戏的最新日志"
	@echo "    make loghelp        # 显示日志工具详细帮助"
	@echo ""
	@echo "💡 常用组合:"
	@echo "  make setup && make server    # 完整设置并启动服务"
	@echo "  make restart                 # 重启开发服务"
	@echo "  make status                  # 检查当前状态"
	@echo "  make force-restart           # 解决进程卡死问题"
	@echo ""
	@echo "⚠️  注意:"
	@echo "  - server 模式：带交互式 shell，适合开发调试"
	@echo "  - dev 模式：纯服务器模式，适合后台运行"
	@echo "  - 启动时会自动从 .env 文件加载环境变量"
	@echo "  - stop: 优雅关闭 (TERM信号，等待5秒)"
	@echo "  - force-stop: 强制关闭 (KILL信号，立即终止)"
	@echo "  - restart: 先优雅停止再启动"
	@echo "  - force-restart: 先强制停止再启动"

# 创建日志目录
logs:
	mkdir -p logs

# 查看日志文件
logs-view:
	@echo "=== System Log ==="
	@tail -n 20 logs/system.log 2>/dev/null || echo "No system log found"
	@echo "\n=== Cypridina Log ==="
	@tail -n 20 logs/cypridina.log 2>/dev/null || echo "No cypridina log found"
	@echo "\n=== Racing Log ==="
	@tail -n 20 logs/racing.log 2>/dev/null || echo "No racing log found"
	@echo "\n=== Teen Log ==="
	@tail -n 20 logs/teen.log 2>/dev/null || echo "No teen log found"

# 清理日志文件
logs-clean:
	rm -f logs/*.log*

# 跟踪指定游戏的最新日志文件
# 用法: make logcat GAME=22 或 make logcat 22
logcat:
	@GAME_ID=$${GAME:-$(filter-out $@,$(MAKECMDGOALS))}; \
	if [ -z "$$GAME_ID" ]; then \
		echo "🎮 游戏日志跟踪工具"; \
		echo ""; \
		echo "用法:"; \
		echo "  make logcat 22        # 跟踪游戏22的最新日志"; \
		echo "  make logcat GAME=22   # 同上"; \
		echo ""; \
		echo "其他命令:"; \
		echo "  make loglist          # 列出所有可用的游戏日志"; \
		echo "  make loglist 22       # 列出游戏22的所有日志"; \
		echo "  make logshow 22       # 显示游戏22最新日志的最后50行"; \
		echo "  make loghelp          # 显示详细帮助信息"; \
		exit 1; \
	fi; \
	GAME_DIR="logs/games"; \
	if [ ! -d "$$GAME_DIR" ]; then \
		echo "❌ 错误: 游戏日志目录不存在: $$GAME_DIR"; \
		exit 1; \
	fi; \
	echo "🔍 正在查找游戏 $$GAME_ID 的最新日志..."; \
	LATEST_LOG=$$(find "$$GAME_DIR" -name "game_$${GAME_ID}_*.log" -type f -printf '%T@ %p\n' 2>/dev/null | sort -nr | head -1 | cut -d' ' -f2- || \
		find "$$GAME_DIR" -name "game_$${GAME_ID}_*.log" -type f -exec stat -c '%Y %n' {} \; 2>/dev/null | sort -nr | head -1 | cut -d' ' -f2-); \
	if [ -z "$$LATEST_LOG" ]; then \
		echo "❌ 未找到游戏 $$GAME_ID 的日志文件"; \
		echo ""; \
		echo "💡 提示: 使用 'make loglist' 查看所有可用的游戏日志"; \
		exit 1; \
	fi; \
	echo "📁 最新日志文件: $$(basename "$$LATEST_LOG")"; \
	echo "📅 修改时间: $$(stat -c '%y' "$$LATEST_LOG" 2>/dev/null | cut -d'.' -f1 || stat -f '%Sm' "$$LATEST_LOG" 2>/dev/null || echo '未知')"; \
	echo "📊 文件大小: $$(du -h "$$LATEST_LOG" 2>/dev/null | cut -f1 || echo '未知')"; \
	echo "🚀 开始跟踪日志 (按 Ctrl+C 退出)"; \
	echo "=========================================="; \
	tail -f "$$LATEST_LOG"

# 列出游戏日志文件
# 用法: make loglist 或 make loglist 22
loglist:
	@GAME_ID=$${GAME:-$(filter-out $@,$(MAKECMDGOALS))}; \
	GAME_DIR="logs/games"; \
	if [ ! -d "$$GAME_DIR" ]; then \
		echo "❌ 错误: 游戏日志目录不存在: $$GAME_DIR"; \
		exit 1; \
	fi; \
	if [ -z "$$GAME_ID" ]; then \
		echo "📋 所有游戏日志文件:"; \
		echo ""; \
		find "$$GAME_DIR" -name "game_*.log" -type f -printf '%TY-%Tm-%Td %TH:%TM  %8s  %f\n' 2>/dev/null | sort -r | head -20 || \
		find "$$GAME_DIR" -name "game_*.log" -type f -exec stat -c '%y %s %n' {} \; 2>/dev/null | sort -r | head -20 | awk '{printf "%s %s  %8s  %s\n", $$1, $$2, $$3, $$4}' | sed 's|.*/||'; \
		echo ""; \
		echo "💡 使用 'make loglist 22' 查看特定游戏的日志"; \
	else \
		echo "📋 游戏 $$GAME_ID 的日志文件:"; \
		echo ""; \
		find "$$GAME_DIR" -name "game_$${GAME_ID}_*.log" -type f -printf '%TY-%Tm-%Td %TH:%TM  %8s  %f\n' 2>/dev/null | sort -r || \
		find "$$GAME_DIR" -name "game_$${GAME_ID}_*.log" -type f -exec stat -c '%y %s %n' {} \; 2>/dev/null | sort -r | awk '{printf "%s %s  %8s  %s\n", $$1, $$2, $$3, $$4}' | sed 's|.*/||'; \
		echo ""; \
		echo "💡 使用 'make logcat $$GAME_ID' 跟踪最新日志"; \
	fi

# 显示指定游戏最新日志的最后几行
# 用法: make logshow 22 或 make logshow GAME=22 LINES=100
logshow:
	@GAME_ID=$${GAME:-$(filter-out $@,$(MAKECMDGOALS))}; \
	LINES=$${LINES:-50}; \
	if [ -z "$$GAME_ID" ]; then \
		echo "🎮 游戏日志查看工具"; \
		echo ""; \
		echo "用法:"; \
		echo "  make logshow 22           # 显示游戏22最新日志的最后50行"; \
		echo "  make logshow 22 LINES=100 # 显示最后100行"; \
		echo "  make logshow GAME=22      # 同上"; \
		exit 1; \
	fi; \
	GAME_DIR="logs/games"; \
	if [ ! -d "$$GAME_DIR" ]; then \
		echo "❌ 错误: 游戏日志目录不存在: $$GAME_DIR"; \
		exit 1; \
	fi; \
	echo "🔍 正在查找游戏 $$GAME_ID 的最新日志..."; \
	LATEST_LOG=$$(find "$$GAME_DIR" -name "game_$${GAME_ID}_*.log" -type f -printf '%T@ %p\n' 2>/dev/null | sort -nr | head -1 | cut -d' ' -f2- || \
		find "$$GAME_DIR" -name "game_$${GAME_ID}_*.log" -type f -exec stat -c '%Y %n' {} \; 2>/dev/null | sort -nr | head -1 | cut -d' ' -f2-); \
	if [ -z "$$LATEST_LOG" ]; then \
		echo "❌ 未找到游戏 $$GAME_ID 的日志文件"; \
		echo ""; \
		echo "💡 提示: 使用 'make loglist' 查看所有可用的游戏日志"; \
		exit 1; \
	fi; \
	echo "📁 最新日志文件: $$(basename "$$LATEST_LOG")"; \
	echo "📅 修改时间: $$(stat -c '%y' "$$LATEST_LOG" 2>/dev/null | cut -d'.' -f1 || stat -f '%Sm' "$$LATEST_LOG" 2>/dev/null || echo '未知')"; \
	echo "📊 文件大小: $$(du -h "$$LATEST_LOG" 2>/dev/null | cut -f1 || echo '未知')"; \
	echo "📄 显示最后 $$LINES 行:"; \
	echo "=========================================="; \
	tail -n "$$LINES" "$$LATEST_LOG"

# 显示详细帮助信息
loghelp:
	@echo "🎮 游戏日志管理工具 - 详细帮助"; \
	echo ""; \
	echo "📋 可用命令:"; \
	echo ""; \
	echo "  🔍 查看和跟踪日志:"; \
	echo "    make logcat 22        # 实时跟踪游戏22的最新日志 (tail -f)"; \
	echo "    make logshow 22       # 显示游戏22最新日志的最后50行"; \
	echo "    make logshow 22 LINES=100  # 显示最后100行"; \
	echo ""; \
	echo "  📋 列出日志文件:"; \
	echo "    make loglist          # 列出所有游戏的日志文件"; \
	echo "    make loglist 22       # 列出游戏22的所有日志文件"; \
	echo ""; \
	echo "  🧹 日志管理:"; \
	echo "    make logs-clean       # 清理所有日志文件"; \
	echo "    make logs-view        # 查看系统日志摘要"; \
	echo ""; \
	echo "📝 使用示例:"; \
	echo ""; \
	echo "  # 实时监控龙虎斗游戏日志"; \
	echo "  make logcat 22"; \
	echo ""; \
	echo "  # 查看最近的错误信息"; \
	echo "  make logshow 22 | grep -i error"; \
	echo ""; \
	echo "  # 查看特定时间段的日志"; \
	echo "  make logshow 22 LINES=1000 | grep '17:30'"; \
	echo ""; \
	echo "💡 提示:"; \
	echo "  - 游戏22通常是龙虎斗游戏"; \
	echo "  - 日志文件按创建时间自动排序"; \
	echo "  - 使用 Ctrl+C 可以退出 logcat 跟踪"; \
	echo "  - 日志文件位于 logs/games/ 目录下"

# 允许传递数字参数给 logcat, loglist, logshow
%:
	@: