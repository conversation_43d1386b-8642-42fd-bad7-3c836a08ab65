defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts.User do
  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cyp<PERSON>ina.Accounts,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [AshAuthentication, AshPaperTrail.Resource],
    data_layer: AshPostgres.DataLayer,
    notifiers: [Ash.Notifier.PubSub]

  require Logger

  authentication do
    add_ons do
      log_out_everywhere do
        apply_on_password_change? true
      end

      confirmation :confirm_new_user do
        monitor_fields [:email]
        confirm_on_create? true
        confirm_on_update? false
        require_interaction? true
        confirmed_at_field :confirmed_at

        auto_confirm_actions [
          :sign_in_with_magic_link,
          :reset_password_with_token,
          :register_with_username,
          :register_with_phone
        ]

        sender Cy<PERSON><PERSON><PERSON>.Accounts.User.Senders.SendNewUserConfirmationEmail
      end
    end

    tokens do
      enabled? true
      token_resource Cypridina.Accounts.Token
      signing_secret <PERSON><PERSON><PERSON>ina.Secrets
      store_all_tokens? true
      require_token_presence_for_authentication? true
    end

    strategies do
      password :username do
        identity_field :username
        hashed_password_field :hashed_password
        sign_in_tokens_enabled? true
        sign_in_action_name :sign_in_with_username

        resettable do
          sender <PERSON><PERSON><PERSON><PERSON>.Accounts.User.Senders.SendPasswordResetEmail
          # these configurations will be the default in a future release
          password_reset_action_name :reset_password_with_token
          # request_password_reset_action_name :request_password_reset_token
        end
      end

      api_key :api_key do
        api_key_relationship :valid_api_keys
        api_key_hash_attribute :api_key_hash
      end

      # password :email do
      #   identity_field :email
      #   hashed_password_field :hashed_password
      #   sign_in_tokens_enabled? true
      #   sign_in_action_name :sign_in_with_email

      #   resettable do
      #     sender Cypridina.Accounts.User.Senders.SendPasswordResetEmail
      #     #   # these configurations will be the default in a future release
      #     password_reset_action_name :reset_password_with_token
      #     request_password_reset_action_name :request_password_reset_token
      #   end
      # end
    end
  end

  postgres do
    table "users"
    repo Cypridina.Repo
  end

  paper_trail do
    # default is :uuid
    primary_key_type(:uuid_v7)

    # default is :snapshot
    change_tracking_mode(:changes_only)

    # default is false
    store_action_name?(true)

    # the primary keys are always ignored
    ignore_attributes([:inserted_at, :updated_at, :last_offline_at])

    # default is []
    ignore_actions([:destroy])

    # 确保多租户属性在版本资源中也存在
    attributes_as_attributes([:channel_id])
  end

  # 定义默认加载的字段
  resource do
  end

  code_interface do
    define :get_by_username, action: :read, get_by_identity: :global_username
    define :get_by_numeric_id, action: :read, get_by_identity: :unique_numeric_id
    define :get_by_phone, action: :read, get_by_identity: :unique_phone
    define :get_by_email, action: :read, get_by_identity: :unique_email
    define :get_by_id, action: :read, get_by: :id
    define :sign_in_with_token
    define :create_guest_user, action: :create_guest_user
    define :register_with_username, action: :register_with_username
    define :register_with_phone, action: :register_with_phone
    define :bind_phone, action: :bind_phone
    define :set_online, action: :set_online
    define :set_offline, action: :set_offline
    define :read
  end

  actions do
    defaults [:read, update: :*, create: :*]

    read :get_by_subject do
      description "通过JWT中的subject声明获取用户"
      argument :subject, :string, allow_nil?: false
      get? true
      prepare AshAuthentication.Preparations.FilterBySubject
    end

    update :change_password do
      # Use this action to allow users to change their password by providing
      # their current password and a new password.

      require_atomic? false
      accept []
      argument :current_password, :string, sensitive?: true, allow_nil?: false

      argument :password, :string,
        sensitive?: true,
        allow_nil?: false,
        constraints: [min_length: 6]

      argument :password_confirmation, :string, sensitive?: true, allow_nil?: false

      validate confirm(:password, :password_confirmation)

      validate {AshAuthentication.Strategy.Password.PasswordValidation,
                strategy_name: :username, password_argument: :current_password}

      change {AshAuthentication.Strategy.Password.HashPasswordChange, strategy_name: :username}
    end

    read :sign_in_with_email do
      description "尝试使用邮箱和密码登录"
      get? true

      argument :email, :ci_string do
        description "用于检索用户的邮箱"
        allow_nil? false
      end

      argument :password, :string do
        description "用于验证匹配用户的密码"
        allow_nil? false
        sensitive? true
      end

      # validates the provided email and password and generates a token
      prepare AshAuthentication.Strategy.Password.SignInPreparation

      metadata :token, :string do
        description "可用于认证用户的JWT令牌"
        allow_nil? false
      end
    end

    # read :sign_in_with_username do
    #   description "使用用户名和密码登录"
    #   get? true

    #   argument :username, :ci_string do
    #     description "用于检索用户的用户名"
    #     allow_nil? false
    #   end

    #   argument :password, :string do
    #     description "用于验证的密码"
    #     allow_nil? false
    #     sensitive? true
    #   end

    #   # 明确指定策略名称为 :username
    #   prepare {AshAuthentication.Strategy.Password.SignInPreparation, strategy_name: :username}
    #   # prepare build(load: [:is_admin, :is_agent, :role_name, :points])

    #   metadata :token, :string do
    #     description "用于认证的JWT令牌"
    #     allow_nil? false
    #   end
    # end

    read :sign_in_with_token do
      # In the generated sign in components, we validate the
      # email and password directly in the LiveView
      # and generate a short-lived token that can be used to sign in over
      # a standard controller action, exchanging it for a standard token.
      # This action performs that exchange. If you do not use the generated
      # liveviews, you may remove this action, and set
      # `sign_in_tokens_enabled? false` in the password strategy.

      description "尝试使用短期登录令牌登录"
      get? true

      argument :token, :string do
        description "短期登录令牌"
        allow_nil? false
        sensitive? true
      end

      # validates the provided sign in token and generates a token
      prepare AshAuthentication.Strategy.Password.SignInWithTokenPreparation

      metadata :token, :string do
        description "可用于认证用户的JWT令牌"
        allow_nil? false
      end
    end

    create :register_with_username do
      description "使用用户名和密码注册新用户"

      accept [
        :email,
        :confirmed_at,
        :permission_level,
        :agent_level
      ]

      # 添加用户名参数
      argument :username, :ci_string do
        allow_nil? false

        constraints min_length: 3,
                    max_length: 20,
                    # 只允许字母、数字和下划线
                    match: "^[a-zA-Z0-9_]+$"
      end

      argument :password, :string do
        description "用户密码"
        allow_nil? false
        constraints min_length: 6
        sensitive? true
      end

      argument :password_confirmation, :string do
        description "确认密码"
        allow_nil? false
        sensitive? true
      end

      argument :asset, :map do
        description "用户资产初始化数据"
        allow_nil? true
        default %{points: 0}
      end

      # 设置属性
      change set_attribute(:username, arg(:username))

      # 生成数字ID - 使用 force_change_attribute 来避免验证后修改的错误
      change before_action(fn changeset, _context ->
               uuid = Ash.Changeset.get_attribute(changeset, :id)
               numeric_id = generate_short_numeric_id_from_uuid(uuid)

               changeset
               |> Ash.Changeset.force_change_attribute(:numeric_id, numeric_id)
             end)

      # 哈希密码
      change AshAuthentication.Strategy.Password.HashPasswordChange

      # 生成认证令牌
      change AshAuthentication.GenerateTokenChange

      # 验证密码确认
      validate AshAuthentication.Strategy.Password.PasswordConfirmationValidation

      # 使用 manage_relationship 初始化用户资料
      change after_action(fn changeset, user, _context ->
               case Cypridina.Accounts.UserProfile.create_initial_profile(%{
                      user_id: user.id,
                      username: user.username
                    }) do
                 {:ok, _profile} -> {:ok, user}
                 # 忽略创建失败，不影响用户创建
                 {:error, _reason} -> {:ok, user}
               end
             end)

      # 初始化用户幸运值记录
      change after_action(fn changeset, user, _context ->
               case Teen.Services.LuckService.update_luck(user.numeric_id, -1, "teen_patti") do
                 {:ok, _luck_record} -> {:ok, user}
                 # 忽略创建失败，不影响用户创建
                 {:error, _reason} -> {:ok, user}
               end
             end)

      # 创建用户积分账户
      # change after_action(fn changeset, user, _context ->
      #          account = Cypridina.Ledger.create_user_account(user.id, :XAA)
      #          {:ok, user}
      #        end)

      metadata :token, :string do
        description "用于认证的JWT令牌"
        allow_nil? false
      end
    end

    # 手机注册
    create :register_with_phone do
      accept [:phone]

      argument :verification_code, :string, allow_nil?: false
      argument :password, :string, allow_nil?: true, sensitive?: true
      argument :password_confirmation, :string, allow_nil?: false, sensitive?: true

      # 生成数字ID
      change before_action(fn changeset, _context ->
               uuid = Ash.Changeset.get_attribute(changeset, :id)
               numeric_id = generate_numeric_id_from_uuid(uuid)
               phone = Ash.Changeset.get_attribute(changeset, :phone)
               username = "Phone#{phone}"

               changeset
               |> Ash.Changeset.force_change_attribute(:numeric_id, numeric_id)
               |> Ash.Changeset.force_change_attribute(:username, username)
               |> Ash.Changeset.force_change_attribute(:phone_verified_at, DateTime.utc_now())
             end)

      change set_context(%{strategy_name: :username})

      # 哈希密码
      change {AshAuthentication.Strategy.Password.HashPasswordChange, strategy: :username}

      # 生成认证令牌
      change AshAuthentication.GenerateTokenChange

      # 验证密码确认
      validate {AshAuthentication.Strategy.Password.PasswordConfirmationValidation,
                strategy: :username}

      # 创建用户积分账户
      change after_action(fn changeset, user, _context ->
               Cypridina.Accounts.UserProfile.create_initial_profile(%{
                 user_id: user.id,
                 username: user.username
               })

               #  account = Cypridina.Ledger.create_user_account(user.id, :XAA)

               Cypridina.Accounts.add_points(user.id, 5_000_000,
                 transaction_type: :bonus,
                 description: "新用户注册奖励",
                 metadata: %{
                   "operation" => "new_user_bonus",
                   "bonus_type" => "registration",
                   "amount" => 50000
                 }
               )

               {:ok, user}
             end)

      # 初始化用户幸运值记录
      change after_action(fn changeset, user, _context ->
               case Teen.Services.LuckService.update_luck(user.numeric_id, -1, "teen_patti") do
                 {:ok, _luck_record} -> {:ok, user}
                 # 忽略创建失败，不影响用户创建
                 {:error, _reason} -> {:ok, user}
               end
             end)

      metadata :token, :string do
        description "用于认证的JWT令牌"
        allow_nil? false
      end
    end

    create :create_guest_user do
      description "创建游客用户（快速登录）"
      accept []

      # 生成游客用户名和邮箱
      change before_action(fn changeset, context ->
               # 生成数字ID
               uuid = Ash.Changeset.get_attribute(changeset, :id)
               numeric_id = generate_short_numeric_id_from_uuid(uuid)
               guest_username = "Guest#{numeric_id}"

               changeset
               |> Ash.Changeset.force_change_attribute(:numeric_id, numeric_id)
               |> Ash.Changeset.force_change_attribute(:username, guest_username)
               |> Ash.Changeset.force_change_attribute(
                 :hashed_password,
                 Bcrypt.hash_pwd_salt("guest_password_#{numeric_id}")
               )
             end)

      # 创建游客积分账户并添加初始积分
      change after_action(fn changeset, user, _context ->
               Cypridina.Accounts.UserProfile.create_initial_profile(%{
                 user_id: user.id,
                 username: user.username
               })

               Cypridina.Accounts.add_points(user.id, 5_000_000,
                 transaction_type: :bonus,
                 description: "新用户注册奖励",
                 metadata: %{
                   "operation" => "new_user_bonus",
                   "bonus_type" => "registration",
                   "amount" => 50000
                 }
               )

               {:ok, user}
             end)

      # 初始化用户幸运值记录
      change after_action(fn changeset, user, _context ->
               case Teen.Services.LuckService.update_luck(user.numeric_id, -1, "teen_patti") do
                 {:ok, _luck_record} -> {:ok, user}
                 # 忽略创建失败，不影响用户创建
                 {:error, _reason} -> {:ok, user}
               end
             end)

      change set_context(%{strategy_name: :username})

      # 生成认证令牌
      change AshAuthentication.GenerateTokenChange

      metadata :token, :string do
        description "用于认证的JWT令牌"
        allow_nil? false
      end
    end

    action :request_password_reset_token do
      description "如果用户存在，向其发送密码重置说明"

      argument :email, :ci_string do
        allow_nil? false
      end

      # creates a reset token and invokes the relevant senders
      run {AshAuthentication.Strategy.Password.RequestPasswordReset, action: :get_by_username}
    end

    # 添加通过用户名查询用户的操作
    read :get_by_username do
      description "查找指定用户名的用户"
      argument :username, :ci_string, allow_nil?: false
      get? true
      filter expr(username == ^arg(:username))
    end

    update :reset_password_with_token do
      argument :reset_token, :string do
        allow_nil? false
        sensitive? true
      end

      argument :password, :string do
        description "用户的明文密码建议"
        allow_nil? false
        constraints min_length: 8
        sensitive? true
      end

      argument :password_confirmation, :string do
        description "用户的明文密码建议（再次输入）"
        allow_nil? false
        sensitive? true
      end

      # validates the provided reset token
      validate AshAuthentication.Strategy.Password.ResetTokenValidation

      # validates that the password matches the confirmation
      validate AshAuthentication.Strategy.Password.PasswordConfirmationValidation

      # Hashes the provided password
      change AshAuthentication.Strategy.Password.HashPasswordChange

      # Generates an authentication token for the user
      change AshAuthentication.GenerateTokenChange
    end

    update :update_permission_level do
      description "更新用户权限级别"
      accept [:permission_level]

      argument :permission_level, :integer do
        allow_nil? false
        constraints min: 0, max: 2
      end

      change set_attribute(:permission_level, arg(:permission_level))
    end

    update :update_agent_level do
      description "更新用户代理等级"
      accept [:agent_level]

      argument :agent_level, :integer do
        allow_nil? false
        constraints min: -1
      end

      change set_attribute(:agent_level, arg(:agent_level))
    end

    update :admin_reset_password do
      description "管理员重置用户密码（无需当前密码验证）"
      require_atomic? false
      accept []

      argument :password, :string,
        sensitive?: true,
        allow_nil?: false,
        constraints: [min_length: 6]

      argument :password_confirmation, :string, sensitive?: true, allow_nil?: false

      validate confirm(:password, :password_confirmation)

      change fn changeset, _context ->
        case Ash.Changeset.get_argument(changeset, :password) do
          nil ->
            changeset

          password ->
            hashed_password = Bcrypt.hash_pwd_salt(password)
            Ash.Changeset.change_attribute(changeset, :hashed_password, hashed_password)
        end
      end
    end

    read :get_admins do
      description "获取所有管理员用户"
      filter expr(permission_level >= 1)
    end

    read :get_super_admins do
      description "获取所有超级管理员用户"
      filter expr(permission_level == 2)
    end

    read :sign_in_with_api_key do
      argument :api_key, :string, allow_nil?: false
      prepare AshAuthentication.Strategy.ApiKey.SignInPreparation
    end

    # 绑定手机号
    update :bind_phone do
      accept [:phone, :phone_verified_at]
      require_atomic? false

      argument :verification_code, :string, allow_nil?: false

      # 验证手机验证码
      validate fn changeset, _context ->
        phone = Ash.Changeset.get_attribute(changeset, :phone)
        code = Ash.Changeset.get_argument(changeset, :verification_code)

        # 如果验证码是 "verified"，说明已经在协议层验证过了
        if code == "verified" do
          :ok
        else
          case Cypridina.Communications.VerificationCode.verify_code(%{
                 phone_number: phone,
                 code: code,
                 code_type: 0
               }) do
            {:ok, true} -> :ok
            {:ok, false} -> {:error, field: :verification_code, message: "验证码错误或已过期"}
            {:error, reason} -> {:error, field: :verification_code, message: reason}
          end
        end
      end

      change fn changeset, _context ->
        phone = Ash.Changeset.get_attribute(changeset, :phone)

        changeset
        |> Ash.Changeset.change_attribute(:phone_verified_at, DateTime.utc_now())
      end
    end

    update :bind_email do
      accept [:email]
      require_atomic? false
      # argument :verification_code, :string, allow_nil?: false

      # # 验证手机验证码
      # validate fn changeset, _context ->
      #   phone = Ash.Changeset.get_attribute(changeset, :phone)
      #   code = Ash.Changeset.get_argument(changeset, :verification_code)

      #   # 如果验证码是 "verified"，说明已经在协议层验证过了
      #   if code == "verified" do
      #     :ok
      #   else
      #     case Cypridina.Communications.VerificationCode.verify_code(%{
      #            phone_number: phone,
      #            code: code,
      #            code_type: 0
      #          }) do
      #       {:ok, true} -> :ok
      #       {:ok, false} -> {:error, field: :verification_code, message: "验证码错误或已过期"}
      #       {:error, reason} -> {:error, field: :verification_code, message: reason}
      #     end
      #   end
      # end
    end

    # 设置用户在线状态
    update :set_online do
      description "设置用户为在线状态"
      accept []

      change set_attribute(:last_offline_at, nil)
    end

    # 设置用户离线状态
    update :set_offline do
      description "设置用户为离线状态"
      accept []

      change set_attribute(:last_offline_at, &DateTime.utc_now/0)
    end
  end

  policies do
    bypass AshAuthentication.Checks.AshAuthenticationInteraction do
      authorize_if always()
    end

    # # 允许任何人创建用户（包括代理创建下线）
    # policy action_type(:create) do
    #   authorize_if always()
    # end

    # # 允许用户读取自己的信息
    # policy action_type(:read) do
    #   authorize_if expr(id == ^actor(:id))
    # end

    # # 允许用户更新自己的信息
    # policy action_type(:update) do
    #   authorize_if expr(id == ^actor(:id))
    # end

    # 默认拒绝其他操作
    policy always() do
      authorize_if always()
    end
  end

  pub_sub do
    module CypridinaWeb.Endpoint
    prefix "user"

    # 发布用户变化事件，使用 publish_all 来发布到多个主题
    # 这将发布到 "user" 和 "user:id" 主题
    publish_all :create, [[:id, nil]]
    publish_all :update, [[:id, nil]]
    publish_all :destroy, [[:id, nil]]
  end

  preparations do
    prepare build(load: [:is_admin, :is_agent, :role_name, :profile])
  end

  multitenancy do
    strategy :attribute
    attribute :channel_id
    global? true
  end

  attributes do
    uuid_primary_key :id

    # 添加数字ID属性
    attribute :numeric_id, :integer do
      public? true
      allow_nil? false
      description "用户的可读数字ID"
    end

    # 添加用户名属性
    attribute :username, :ci_string do
      public? true
      allow_nil? true
      description "用户名，用于登录和显示"
    end

    attribute :email, :ci_string do
      allow_nil? true
      public? true
    end

    # 手机号字段
    attribute :phone, :string do
      allow_nil? true
      public? true
      description "手机号码，包含国家代码，如：+8613800138000"
      constraints max_length: 20
    end

    attribute :phone_verified_at, :utc_datetime do
      allow_nil? true
      public? true
      description "手机号验证时间"
    end

    # 在线状态标记字段 - 通过最后离线时间判断
    attribute :last_offline_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后离线时间，为空表示在线，有值表示离线"
    end

    attribute :hashed_password, :string do
      allow_nil? false
      sensitive? true
    end

    attribute :confirmed_at, :utc_datetime_usec

    # 代理系统相关字段
    attribute :agent_level, :integer do
      allow_nil? false
      default -1
      public? true
      description "代理等级：-1=不是代理，0=根代理，>0=下级代理"
      constraints min: -1
    end

    # 用户权限级别字段
    attribute :permission_level, :integer do
      allow_nil? false
      default 0
      public? true
      description "权限级别: 0-普通用户, 1-管理员, 2-超级管理员"
      constraints min: 0, max: 2
    end

    # # 渠道ID字段
    attribute :channel_id, :string do
      allow_nil? true
      public? true
      description "用户所属渠道ID"
    end

    timestamps()
  end

  relationships do
    # 用户资料
    has_one :profile, Cypridina.Accounts.UserProfile do
      public? true
      source_attribute :id
      destination_attribute :user_id
    end

    # 用户设备
    has_many :devices, Cypridina.Accounts.UserDevice do
      public? true
      source_attribute :id
      destination_attribute :user_id
    end

    # 代理关系
    has_many :agent_relationships, Cypridina.Accounts.AgentRelationship do
      public? true
      source_attribute :id
      destination_attribute :subordinate_id
    end

    has_many :subordinate_relationships, Cypridina.Accounts.AgentRelationship do
      public? true
      source_attribute :id
      destination_attribute :agent_id
    end

    has_many :valid_api_keys, Cypridina.Accounts.ApiKey do
      filter expr(valid)
    end

    # 登录渠道关联
    belongs_to :channel, Cypridina.Accounts.Channel do
      public? true
      source_attribute :channel_id
      destination_attribute :channel_id
    end
  end

  calculations do
    # 添加显示ID的计算字段
    calculate :display_id, :string, expr("用户#{numeric_id}")

    # 用户角色显示
    calculate :role_name,
              :string,
              expr(
                cond do
                  permission_level == 0 -> "普通玩家"
                  permission_level == 1 -> "管理员"
                  permission_level == 2 -> "超级管理员"
                  # 默认值
                  true -> "未知角色"
                end
              )

    # 是否为管理员（兼容性）
    calculate :is_admin, :boolean, expr(permission_level >= 1)

    # 是否为超级管理员（兼容性）
    calculate :is_super_admin, :boolean, expr(permission_level >= 1)

    # 代理相关计算字段
    calculate :is_agent, :boolean, expr(agent_level >= 0)

    calculate :is_root_agent, :boolean, expr(agent_level == 0)

    # 是否可以访问后台管理
    calculate :can_access_admin, :boolean, expr(permission_level >= 1 or agent_level >= 0)

    # 用户积分计算字段 - 从复式记账系统获取实时余额
    calculate :points, :integer do
      public? true
      description "用户当前积分余额"

      calculation fn records, _context ->
        # 为每个用户记录计算积分
        Enum.map(records, fn user -> Cypridina.Accounts.get_user_points(user.id) end)
      end
    end

    # 在线状态计算字段
    calculate :is_online, :boolean do
      public? true
      description "用户是否在线，基于last_offline_at字段判断"

      calculation expr(is_nil(last_offline_at))
    end

    # 渠道名称计算字段（可以根据需要扩展）
    calculate :channel_name, :string do
      public? true
      description "渠道名称显示"

      calculation expr(
                    cond do
                      is_nil(channel_id) -> "默认渠道"
                      true -> "渠道#{channel_id}"
                    end
                  )
    end
  end

  identities do
    # 全局唯一约束（适用于管理员和系统账户）
    identity :global_username, [:username], all_tenants?: true
    # 添加用户名唯一性约束
    # identity :unique_username, [:username]

    # 其他约束保持不变
    identity :unique_email, [:email]
    identity :unique_numeric_id, [:numeric_id]
    identity :unique_phone, [:phone]
  end

  # 私有函数：根据UUID生成纯数字ID
  defp generate_numeric_id_from_uuid(uuid) when is_binary(uuid) do
    # 方案1：使用CRC32哈希（推荐）
    # 移除UUID中的连字符，然后计算CRC32
    uuid_clean = String.replace(uuid, "-", "")

    # 将UUID转换为二进制，然后计算CRC32
    uuid_binary = Base.decode16!(uuid_clean, case: :mixed)
    crc32 = :erlang.crc32(uuid_binary)

    # 确保结果是正数，并限制在合理范围内（8位数字）
    (abs(crc32) |> rem(100_000_000)) + 10_000_000
  end

  # 备用方案：如果需要更短的数字ID
  defp generate_short_numeric_id_from_uuid(uuid) when is_binary(uuid) do
    # 取UUID的前8个字符，转换为整数
    uuid
    |> String.replace("-", "")
    |> String.slice(0, 8)
    |> String.to_integer(16)
    # 限制为8位数字
    |> rem(100_000_000)
    # 确保是8位数字
    |> Kernel.+(10_000_000)
  end

  # 备用方案：基于时间戳的数字ID
  defp _generate_timestamp_based_numeric_id(uuid) when is_binary(uuid) do
    # 获取当前时间戳（微秒）
    timestamp = System.system_time(:microsecond)

    # 取UUID的一部分作为随机种子
    uuid_seed =
      uuid
      |> String.replace("-", "")
      |> String.slice(0, 4)
      |> String.to_integer(16)

    # 组合时间戳和UUID种子
    ((timestamp + uuid_seed) |> rem(100_000_000)) + 10_000_000
  end
end
