defmodule Teen.Live.UserManagementLive do
  @moduledoc """
  用户管理页面 - 移植自RacingGame.Live.AdminPanel.UserManagementComponent

  提供用户管理功能，仅限管理员使用
  """

  use CypridinaWeb, :live_view

  alias CypridinaWeb.AuthHelper
  alias Cypridina.Accounts.User
  alias Cypridina.Accounts.AgentRelationship
  alias Cypridina.Utils.TimeHelper
  alias CypridinaWeb.Components.PointsHistoryComponent
  require Ash.Query

  def mount(_params, _session, socket) do
    if AuthHelper.has_permission?(socket.assigns.current_user, :admin) do
      socket =
        socket
        |> assign(:page_title, "用户管理")
        |> assign(:fluid?, true)
        |> assign_defaults()
        |> load_users_data()

      {:ok, socket, layout: {Teen.Layouts, :admin}}
    else
      {:ok, socket |> put_flash(:error, "权限不足") |> redirect(to: "/")}
    end
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    # "normal" 或 "agent"
    |> assign(:search_mode, "normal")
    |> assign(:page, 1)
    |> assign(:per_page, 20)
    |> assign(:page_info, nil)
    |> assign(:show_create_modal, false)
    |> assign(:show_edit_modal, false)
    |> assign(:selected_user, nil)
    |> assign(:create_form, %{})
    |> assign(:edit_form, %{})
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:search_mode, "normal")
      |> assign(:page, 1)
      |> load_users_data()

    {:noreply, socket}
  end

  def handle_event("search_agents", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:search_mode, "agent")
      |> assign(:page, 1)
      |> load_users_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    page = String.to_integer(page)

    socket =
      socket
      |> assign(:page, page)
      |> load_users_data()

    {:noreply, socket}
  end

  def handle_event("show_create_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_create_modal, true)
      |> assign(:create_form, %{
        "username" => "",
        "password" => "",
        "permission_level" => "0",
        "user_type" => "normal",
        "initial_points" => "0"
      })

    {:noreply, socket}
  end

  def handle_event("hide_create_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_create_modal, false)
      |> assign(:create_form, %{})

    {:noreply, socket}
  end

  def handle_event("show_edit_modal", %{"user_id" => user_id}, socket) do
    case get_user_by_id(user_id) do
      {:ok, user} ->
        # 根据用户的 agent_level 确定 user_type
        user_type = if user.agent_level >= 0, do: "agent", else: "normal"

        socket =
          socket
          |> assign(:show_edit_modal, true)
          |> assign(:selected_user, user)
          |> assign(:edit_form, %{
            "permission_level" => to_string(user.permission_level),
            "user_type" => user_type,
            "points_adjustment" => "0",
            "adjustment_reason" => ""
          })

        {:noreply, socket}

      {:error, _} ->
        {:noreply, socket}
    end
  end

  def handle_event("hide_edit_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_edit_modal, false)
      |> assign(:selected_user, nil)

    {:noreply, socket}
  end

  def handle_event("create_user", %{"user" => user_params}, socket) do
    current_user = socket.assigns.current_user

    case create_new_user(user_params, current_user) do
      {:ok, _user} ->
        socket =
          socket
          |> assign(:show_create_modal, false)
          |> load_users_data()
          |> put_flash(:info, "用户创建成功")

        {:noreply, socket}

      {:error, _changeset} ->
        socket = put_flash(socket, :error, "用户创建失败，请检查输入信息")
        {:noreply, socket}
    end
  end

  def handle_event("update_user", %{"user" => user_params}, socket) do
    user = socket.assigns.selected_user
    current_user = socket.assigns.current_user

    case update_user_info(user, user_params, current_user) do
      {:ok, _user} ->
        socket =
          socket
          |> assign(:show_edit_modal, false)
          |> load_users_data()
          |> put_flash(:info, "用户信息更新成功")

        {:noreply, socket}

      {:error, _changeset} ->
        socket = put_flash(socket, :error, "用户信息更新失败")
        {:noreply, socket}
    end
  end

  defp load_users_data(socket) do
    user = socket.assigns.current_user

    if AuthHelper.has_permission?(user, :admin) do
      search_query = socket.assigns.search_query
      search_mode = socket.assigns.search_mode
      page = socket.assigns.page
      per_page = socket.assigns.per_page

      case search_mode do
        "agent" ->
          load_agent_search_data(socket, search_query, page, per_page)

        _ ->
          load_normal_search_data(socket, search_query, page, per_page)
      end
    else
      per_page = socket.assigns.per_page

      socket
      |> assign(:users_data, [])
      |> assign(:page_info, %{total_count: 0, page: 1, per_page: per_page})
    end
  end

  defp load_normal_search_data(socket, search_query, page, per_page) do
    offset = (page - 1) * per_page

    query =
      User
      |> Ash.Query.limit(per_page)
      |> Ash.Query.offset(offset)
      |> Ash.Query.sort(inserted_at: :desc)

    query =
      if search_query != "" do
        Ash.Query.filter(query, contains(username, ^search_query))
      else
        query
      end

    case Ash.read(query) do
      {:ok, users} ->
        total_count = get_total_users_count(search_query)

        socket
        |> assign(:users_data, users)
        |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})

      {:error, _} ->
        socket
        |> assign(:users_data, [])
        |> assign(:page_info, %{total_count: 0, page: page, per_page: per_page})
    end
  end

  defp load_agent_search_data(socket, search_query, page, per_page) do
    require Ash.Query
    import Ash.Expr

    offset = (page - 1) * per_page

    query =
      User
      |> Ash.Query.filter(expr(agent_level >= 0))
      |> Ash.Query.limit(per_page)
      |> Ash.Query.offset(offset)
      |> Ash.Query.sort(inserted_at: :desc)

    query =
      if search_query != "" do
        Ash.Query.filter(query, expr(contains(username, ^search_query)))
      else
        query
      end

    case Ash.read(query) do
      {:ok, users} ->
        total_count = get_total_agents_count(search_query)

        socket
        |> assign(:users_data, users)
        |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})

      {:error, _} ->
        socket
        |> assign(:users_data, [])
        |> assign(:page_info, %{total_count: 0, page: page, per_page: per_page})
    end
  end

  defp get_user_by_id(user_id) do
    User
    |> Ash.Query.filter(id == ^user_id)
    |> Ash.read_one()
  end

  defp get_total_users_count(search_query) do
    query =
      if search_query != "" do
        User |> Ash.Query.filter(contains(username, ^search_query))
      else
        User
      end

    case Ash.count(query) do
      {:ok, count} -> count
      {:error, _} -> 0
    end
  end

  defp get_total_agents_count(search_query) do
    query = User |> Ash.Query.filter(agent_level >= 0)

    query =
      if search_query != "" do
        Ash.Query.filter(query, contains(username, ^search_query))
      else
        query
      end

    case Ash.count(query) do
      {:ok, count} -> count
      {:error, _} -> 0
    end
  end

  defp create_new_user(user_params, _current_user) do
    # 这里需要实现用户创建逻辑
    # 暂时返回成功，实际需要调用User的创建action
    {:ok, %{}}
  end

  defp update_user_info(user, user_params, current_user) do
    # 处理用户类型和权限级别更新
    permission_level = String.to_integer(user_params["permission_level"] || "0")
    user_type = user_params["user_type"] || "normal"

    # 确定agent_level值
    agent_level = if user_type == "agent", do: 0, else: -1

    # 更新用户基本信息
    user_update_result =
      user
      |> Ash.Changeset.for_update(:update, %{
        permission_level: permission_level,
        agent_level: agent_level
      })
      |> Ash.update()

    # 处理积分调整（如果有）
    case user_update_result do
      {:ok, updated_user} ->
        points_adjustment = user_params["points_adjustment"]

        if is_binary(points_adjustment) && points_adjustment != "0" && points_adjustment != "" do
          points = String.to_integer(points_adjustment)
          reason = user_params["adjustment_reason"] || "管理员调整"

          # 调用积分调整action
          case updated_user
               |> Ash.Changeset.for_update(:adjust_points, %{
                 points: points,
                 reason: reason,
                 operator_id: current_user.id
               })
               |> Ash.update() do
            {:ok, _} = result -> result
            error -> error
          end
        else
          user_update_result
        end

      error -> error
    end
  end
end
