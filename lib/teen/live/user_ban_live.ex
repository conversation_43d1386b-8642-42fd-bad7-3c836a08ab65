defmodule Teen.Live.UserBanLive do
  @moduledoc """
  用户封禁管理页面

  提供用户封禁的创建、查看、编辑和解封功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.BanSystem.UserBan
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "封禁记录"

  @impl Backpex.LiveResource
  def plural_name, do: "封禁管理"

  @impl Backpex.LiveResource
  def fields do
    %{
      user: %{
        module: Backpex.Fields.BelongsTo,
        label: "用户",
        display_field: :username,
        live_resource: Teen.Live.UserLive,
        searchable: true
      },
      ban_type: %{
        module: Backpex.Fields.Select,
        label: "封禁类型",
        options: [
          {"账号封禁", 1},
          {"设备封禁", 2},
          {"IP封禁", 3},
          {"支付封禁", 4}
        ],
        searchable: true
      },
      reason: %{
        module: Backpex.Fields.Textarea,
        label: "封禁原因",
        searchable: true,
        help_text: "详细说明封禁原因"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"已解封", 0},
          {"封禁中", 1}
        ],
        searchable: true
      },
      cash_amount: %{
        module: Backpex.Fields.Number,
        label: "封号时现金数",
        only: [:index, :show],
        help_text: "封号时用户的现金余额"
      },
      bank_amount: %{
        module: Backpex.Fields.Number,
        label: "封号时银行数",
        only: [:index, :show],
        help_text: "封号时用户的银行余额"
      },
      ip_address: %{
        module: Backpex.Fields.Text,
        label: "IP地址",
        help_text: "用户封禁时的IP地址"
      },
      operator_id: %{
        module: Backpex.Fields.BelongsTo,
        label: "操作员",
        display_field: :username,
        live_resource: Cypridina.Accounts.User,
        only: [:index, :show]
      },
      banned_at: %{
        module: Backpex.Fields.DateTime,
        label: "封禁时间",
        only: [:index, :show]
      },
      expires_at: %{
        module: Backpex.Fields.DateTime,
        label: "到期时间",
        help_text: "留空表示永久封禁"
      },
      unbanned_at: %{
        module: Backpex.Fields.DateTime,
        label: "解封时间",
        only: [:index, :show]
      },
      unban_operator_id: %{
        module: Backpex.Fields.BelongsTo,
        label: "解封操作员",
        display_field: :username,
        live_resource: Cypridina.Accounts.User,
        only: [:index, :show]
      },
      unban_reason: %{
        module: Backpex.Fields.Textarea,
        label: "解封原因",
        only: [:edit, :show],
        help_text: "解封时填写的原因"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        only: [:show]
      }
    }
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      unban_user: %{
        module: Teen.ResourceActions.UnbanUser
      }
    ]
  end

  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Filters.BanStatusSelect
      },
      ban_type: %{
        module: Teen.Filters.BanTypeSelect
      }
    ]
  end
end
