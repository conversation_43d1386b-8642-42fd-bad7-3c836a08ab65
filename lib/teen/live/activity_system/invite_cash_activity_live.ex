defmodule Teen.Live.ActivitySystem.InviteCashActivityLive do
  @moduledoc """
  邀请奖励活动管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.InviteCashActivity
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "邀请奖励活动"

  @impl Backpex.LiveResource
  def plural_name, do: "邀请奖励活动"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      activity_name: %{
        module: Backpex.Fields.Text,
        label: "活动名称"
      },
      invite_reward: %{
        module: Backpex.Fields.Number,
        label: "邀请奖励"
      },
      invitee_reward: %{
        module: Backpex.Fields.Number,
        label: "被邀请人奖励"
      },
      min_invitee_recharge: %{
        module: Backpex.Fields.Number,
        label: "被邀请人最小充值"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"现金", "cash"}
        ]
      },
      max_invites_per_day: %{
        module: Backpex.Fields.Number,
        label: "每日最大邀请数"
      },
      activity_duration_days: %{
        module: Backpex.Fields.Number,
        label: "活动持续天数"
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期"
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
