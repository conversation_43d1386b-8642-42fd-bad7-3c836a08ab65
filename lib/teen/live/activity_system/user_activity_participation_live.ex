defmodule Teen.Live.ActivitySystem.UserActivityParticipationLive do
  @moduledoc """
  用户活动参与记录管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.UserActivityParticipation
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "用户活动参与记录"

  @impl Backpex.LiveResource
  def plural_name, do: "用户活动参与记录"

  @impl Backpex.LiveResource
  def filters do
    [
      activity_type: %{
        module: __MODULE__.ActivityTypeFilter,
        label: "活动类型"
      },
      user_id: %{
        module: __MODULE__.UserIdFilter,
        label: "用户ID"
      }
    ]
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      user_id: %{
        module: Backpex.Fields.Text,
        label: "用户ID"
      },
      activity_type: %{
        module: Backpex.Fields.Select,
        label: "活动类型",
        options: [
          {"每日游戏任务", :game_task},
          {"周卡活动", :weekly_card},
          {"七日登录", :seven_day_task},
          {"VIP礼包", :vip_gift},
          {"充值任务", :recharge_task},
          {"转盘抽奖", :recharge_wheel},
          {"刮刮卡", :scratch_card},
          {"首充礼包", :first_recharge_gift},
          {"亏损返利", :loss_rebate_jar},
          {"邀请奖励", :invite_cash},
          {"绑定奖励", :binding_reward},
          {"免费任务", :free_bonus_task},
          {"CDKey活动", :cdkey_activity},
          {"签到活动", :sign_in_activity}
        ]
      },
      activity_id: %{
        module: Backpex.Fields.Text,
        label: "活动ID",
        only: [:show]
      },

      progress: %{
        module: Backpex.Fields.Number,
        label: "进度"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"活跃", :active},
          {"完成", :completed},
          {"过期", :expired},
          {"暂停", :paused}
        ]
      },
      participation_data: %{
        module: Backpex.Fields.Text,
        label: "参与数据",
        render: fn assigns ->
          formatted_value =
            case assigns.value do
              nil ->
                "无数据"
              map when is_map(map) ->
                # 特别处理刮刮卡数据的显示
                if assigns.item && assigns.item.activity_type == :scratch_card do
                  current_card = Map.get(map, "current_card", 0)
                  current_level = Map.get(map, "current_level", 1)
                  total_recharge = Map.get(map, "total_recharge", "0")
                  claimed_cards = Map.get(map, "claimed_cards", [])
                  has_pending = Map.has_key?(map, "pending_rewards")

                  """
                  当前等级: #{current_level}
                  已刮卡片: #{current_card}/30
                  累计充值: #{total_recharge}分
                  已领取记录: #{length(claimed_cards)}条
                  待领取奖励: #{if has_pending, do: "是", else: "否"}

                  原始数据:
                  #{Jason.encode!(map, pretty: true)}
                  """
                else
                  Jason.encode!(map, pretty: true)
                end
              str when is_binary(str) ->
                str
              _ ->
                "无效数据"
            end

          assigns = assign(assigns, :formatted_value, formatted_value)

          ~H"""
          <pre class="text-sm bg-gray-100 p-2 rounded overflow-x-auto max-h-48 whitespace-pre-wrap"><%= @formatted_value %></pre>
          """
        end,
        except: [:new, :edit]
      },
      participated_at: %{
        module: Backpex.Fields.DateTime,
        label: "参与时间",
        only: [:show]
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        only: [:show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        only: [:show]
      },
      user_recharge_info: %{
        module: Backpex.Fields.Text,
        label: "用户充值信息",
        render: fn assigns ->
          # 只对刮刮卡活动显示充值信息
          if assigns.item && assigns.item.activity_type == :scratch_card do
            case Teen.GameManagement.UserVipRecord.get_by_user_id(assigns.item.user_id) do
              {:ok, vip_record} ->
                recharge_amount =
                  if vip_record.total_recharge_amount do
                    vip_record.total_recharge_amount
                    |> Decimal.div(Decimal.new(100))
                    |> Decimal.to_string()
                  else
                    "0"
                  end

                assigns = assign(assigns, :recharge_amount, recharge_amount)

                ~H"""
                <span class="text-sm">
                  充值: <%= @recharge_amount %> 元
                </span>
                """
              _ ->
                ~H"""
                <span class="text-sm text-gray-500">
                  充值: 0 元
                </span>
                """
            end
          else
            ~H"""
            <span class="text-sm text-gray-500">-</span>
            """
          end
        end,
        only: [:index, :show]
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end

  @impl Backpex.LiveResource
  def item_actions do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :show]
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      reset_scratch_card: %{
        module: Teen.ResourceActions.ResetScratchCard,
        confirm: "确定要解除选中用户的刮刮卡限制吗？"
      },
      add_recharge: %{
        module: Teen.ResourceActions.AddScratchCardRecharge,
        confirm: "确定要为选中用户增加100元充值吗？"
      }
    ]
  end

  defmodule ActivityTypeFilter do
    @moduledoc """
    活动类型筛选器
    """
    use Backpex.Filters.Select

    @impl Backpex.Filter
    def label, do: "活动类型"

    @impl Backpex.Filter
    def can?(_assigns), do: true

    @impl Backpex.Filters.Select
    def prompt, do: "选择活动类型..."

    @impl Backpex.Filters.Select
    def options(_assigns) do
      [
        {"全部", nil},
        {"刮刮卡", :scratch_card},
        {"每日游戏任务", :daily_game_task},
        {"周卡活动", :weekly_card},
        {"七日登录", :seven_day_login},
        {"充值任务", :recharge_task},
        {"VIP礼包", :vip_gift},
        {"转盘抽奖", :spin_wheel},
        {"首充礼包", :first_recharge},
        {"亏损返利", :loss_rebate},
        {"邀请奖励", :invite_reward},
        {"绑定奖励", :binding_reward},
        {"免费任务", :free_task},
        {"CDKey", :cdkey}
      ]
    end
  end

  defmodule UserIdFilter do
    @moduledoc """
    用户ID筛选器
    """
    use Backpex.Filter
    import Phoenix.Component
    import Phoenix.HTML.Form

    @impl Backpex.Filter
    def label, do: "用户ID"

    @impl Backpex.Filter
    def can?(_assigns), do: true

    @impl Backpex.Filter
    def render(assigns) do
      ~H"""
      <input
        type="text"
        name={@form[@field.name].name}
        value={@form[@field.name].value}
        placeholder="输入用户ID..."
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
      />
      """
    end

    @impl Backpex.Filter
    def render_form(assigns) do
      ~H"""
      <input
        type="text"
        name={@form[@field].name}
        value={@form[@field].value}
        placeholder="输入用户ID..."
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
      />
      """
    end

    @impl Backpex.Filter
    def query(query, nil, _live_resource), do: query
    def query(query, "", _live_resource), do: query
    def query(query, value, _live_resource) do
      import Ecto.Query

      where(query, [r], r.user_id == ^value)
    end
  end
end
