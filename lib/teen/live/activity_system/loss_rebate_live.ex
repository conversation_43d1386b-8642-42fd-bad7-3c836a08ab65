defmodule Teen.Live.ActivitySystem.LossRebateLive do
  @moduledoc """
  亏损返利管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.LossRebateJar
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "亏损返利"

  @impl Backpex.LiveResource
  def plural_name, do: "亏损返利"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      rebate_name: %{
        module: Backpex.Fields.Text,
        label: "返利名称"
      },
      min_loss_amount: %{
        module: Backpex.Fields.Number,
        label: "最小亏损金额"
      },
      rebate_percentage: %{
        module: Backpex.Fields.Number,
        label: "返利百分比(%)"
      },
      max_rebate_amount: %{
        module: Backpex.Fields.Number,
        label: "最大返利金额"
      },
      calculation_period: %{
        module: Backpex.Fields.Select,
        label: "计算周期",
        options: [
          {"每日", "daily"},
          {"每周", "weekly"},
          {"每月", "monthly"}
        ]
      },
      rebate_type: %{
        module: Backpex.Fields.Select,
        label: "返利类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"现金", "cash"}
        ]
      },
      auto_distribute: %{
        module: Backpex.Fields.Boolean,
        label: "自动发放"
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
