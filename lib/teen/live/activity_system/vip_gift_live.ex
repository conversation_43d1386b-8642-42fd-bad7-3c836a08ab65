defmodule Teen.Live.ActivitySystem.VipGiftLive do
  @moduledoc """
  VIP礼包管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.VipGift
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "VIP礼包"

  @impl Backpex.LiveResource
  def plural_name, do: "VIP礼包"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      vip_level: %{
        module: Backpex.Fields.Number,
        label: "VIP等级"
      },
      gift_name: %{
        module: Backpex.Fields.Text,
        label: "礼包名称"
      },
      gift_type: %{
        module: Backpex.Fields.Select,
        label: "礼包类型",
        options: [
          {"每日礼包", "daily"},
          {"每周礼包", "weekly"},
          {"每月礼包", "monthly"},
          {"升级礼包", "upgrade"}
        ]
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"道具", "items"}
        ]
      },
      cooldown_hours: %{
        module: Backpex.Fields.Number,
        label: "冷却时间(小时)"
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
