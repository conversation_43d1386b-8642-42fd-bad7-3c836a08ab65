defmodule Teen.Live.ActivitySystem.CdkeyRewardLive do
  @moduledoc """
  CDKey奖励管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.CdkeyActivity
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "CDKey奖励"

  @impl Backpex.LiveResource
  def plural_name, do: "CDKey奖励"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      cdkey_code: %{
        module: Backpex.Fields.Text,
        label: "CDKey代码"
      },
      cdkey_type: %{
        module: Backpex.Fields.Select,
        label: "CDKey类型",
        options: [
          {"通用码", "general"},
          {"限量码", "limited"},
          {"专属码", "exclusive"},
          {"活动码", "event"}
        ]
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"道具", "items"},
          {"现金", "cash"}
        ]
      },
      usage_limit: %{
        module: Backpex.Fields.Number,
        label: "使用次数限制"
      },
      used_count: %{
        module: Backpex.Fields.Number,
        label: "已使用次数",
        readonly: true
      },
      expiry_date: %{
        module: Backpex.Fields.DateTime,
        label: "过期时间"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述"
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
