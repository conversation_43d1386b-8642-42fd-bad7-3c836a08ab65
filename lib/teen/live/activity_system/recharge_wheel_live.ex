defmodule Teen.Live.ActivitySystem.RechargeWheelLive do
  @moduledoc """
  充值转盘管理界面
  """
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.RechargeWheel
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "充值转盘"

  @impl Backpex.LiveResource
  def plural_name, do: "充值转盘"

  @impl Backpex.LiveResource
  def fields do
    %{
      name: %{
        module: Backpex.Fields.Text,
        label: "转盘名称"
      },
      min_recharge_amount: %{
        module: Backpex.Fields.Number,
        label: "最低充值金额"
      },
      jackpot_pool: %{
        module: Backpex.Fields.Number,
        label: "奖金池",
        readonly: true
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", "enabled"},
          {"禁用", "disabled"}
        ]
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end
end
