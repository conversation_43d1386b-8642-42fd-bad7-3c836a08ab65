defmodule Teen.Live.ProductLive do
  @moduledoc """
  商品管理页面

  提供商品的创建、查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ShopSystem.Product
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "商品"

  @impl Backpex.LiveResource
  def plural_name, do: "商品管理"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      name: %{
        module: Backpex.Fields.Text,
        label: "商品名称",
        searchable: true,
        orderable: true
      },
      product_type: %{
        module: Backpex.Fields.Select,
        label: "商品类型",
        options: [
          {"月卡", :monthly_card},
          {"周卡", :weekly_card},
          {"次卡", :play_card},
          {"金币礼包", :coin_package},
          {"VIP礼包", :vip_package},
          {"特殊道具", :special_item},
          {"充值奖励包", :recharge_bonus}
        ],
        searchable: true,
        orderable: true
      },
      category: %{
        module: Backpex.Fields.Text,
        label: "商品分类",
        searchable: true
      },
      sku: %{
        module: Backpex.Fields.Text,
        label: "商品SKU",
        searchable: true,
        readonly: true
      },
      price: %{
        module: Backpex.Fields.Number,
        label: "价格（分）",
        orderable: true
      },
      price_display: %{
        module: Backpex.Fields.Text,
        label: "价格显示",
        readonly: true,
        only: [:index, :show]
      },
      currency: %{
        module: Backpex.Fields.Select,
        label: "货币类型",
        options: [
          {"印度卢比 (₹)", :inr},
          {"美元 ($)", :usd},
          {"人民币 (¥)", :cny}
        ]
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"上架", :active},
          {"下架", :inactive},
          {"售罄", :sold_out}
        ],
        searchable: true,
        orderable: true
      },
      status_display: %{
        module: Backpex.Fields.Text,
        label: "状态显示",
        readonly: true,
        only: [:index, :show]
      },
      sort_order: %{
        module: Backpex.Fields.Number,
        label: "排序顺序",
        orderable: true
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "商品描述",
        only: [:show, :edit, :new]
      },
      product_config: %{
        module: Backpex.Fields.Textarea,
        label: "商品配置 (JSON)",
        only: [:show, :edit, :new],
        render: fn assigns ->
          config_json = Jason.encode!(assigns.value, pretty: true)
          assigns = assign(assigns, :config_json, config_json)

          ~H"""
          <pre class="bg-gray-100 p-2 rounded text-sm overflow-auto max-h-40"><%= @config_json %></pre>
          """
        end
      },
      display_config: %{
        module: Backpex.Fields.Textarea,
        label: "显示配置 (JSON)",
        only: [:show, :edit, :new],
        render: fn assigns ->
          config_json = Jason.encode!(assigns.value, pretty: true)
          assigns = assign(assigns, :config_json, config_json)

          ~H"""
          <pre class="bg-gray-100 p-2 rounded text-sm overflow-auto max-h-40"><%= @config_json %></pre>
          """
        end
      },
      created_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true,
        only: [:index, :show],
        orderable: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true,
        only: [:show],
        orderable: true
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      product_type: %{
        module: Teen.Filters.ProductTypeSelect
      },
      status: %{
        module: Teen.Filters.ProductStatusSelect
      },
      currency: %{
        module: Teen.Filters.CurrencySelect
      }
    ]
  end

  @impl Backpex.LiveResource
  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      }
    ]
  end

  # @impl Backpex.LiveResource
  # def metrics do
  #   [
  #     total_products: %{
  #       module: Backpex.Metrics.Simple,
  #       label: "商品总数",
  #       value: fn _socket ->
  #         case Ash.read(Teen.ShopSystem.Product) do
  #           {:ok, products} -> length(products)
  #           {:error, _} -> 0
  #         end
  #       end
  #     },
  #     active_products: %{
  #       module: Backpex.Metrics.Simple,
  #       label: "上架商品",
  #       value: fn _socket ->
  #         case Teen.ShopSystem.Product.list_active_products() do
  #           {:ok, products} -> length(products)
  #           {:error, _} -> 0
  #         end
  #       end
  #     }
  #   ]
  # end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      create_from_template: %{
        module: Backpex.Actions.Resource,
        label: "从模板创建",
        icon: "hero-document-duplicate",
        handler: fn socket ->
          # 这里可以实现从模板创建商品的逻辑
          {:ok, socket, "功能开发中"}
        end
      }
    ]
  end
end
