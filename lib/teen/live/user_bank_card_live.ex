defmodule Teen.Live.UserBankCardLive do
  @moduledoc """
  用户银行卡管理页面

  提供用户银行卡的查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.PaymentSystem.UserBankCard
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "用户银行卡"

  @impl Backpex.LiveResource
  def plural_name, do: "用户银行卡"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      user: %{
        module: Backpex.Fields.BelongsTo,
        label: "用户",
        display_field: :username,
        live_resource: Teen.Live.UserLive,
        searchable: true,
        help_text: "银行卡所属用户"
      },
      bank: %{
        module: Backpex.Fields.BelongsTo,
        label: "银行",
        display_field: :name,
        live_resource: Teen.Live.BankConfigLive,
        searchable: true,
        help_text: "选择银行"
      },
      account_name: %{
        module: Backpex.Fields.Text,
        label: "账户姓名",
        searchable: true,
        help_text: "银行卡开户姓名"
      },
      account_number: %{
        module: Backpex.Fields.Text,
        label: "银行卡号",
        searchable: true,
        help_text: "银行卡号码",
        render: fn assigns ->
          # 脱敏显示银行卡号
          card_number = assigns.value || ""

          masked_number =
            if String.length(card_number) > 8 do
              first_four = String.slice(card_number, 0, 4)
              last_four = String.slice(card_number, -4, 4)
              "#{first_four}****#{last_four}"
            else
              card_number
            end

          ~H"""
          <span class="font-mono">{masked_number}</span>
          """
        end
      },
      is_default: %{
        module: Backpex.Fields.Boolean,
        label: "默认卡",
        help_text: "是否为默认银行卡"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"禁用", 0},
          {"启用", 1}
        ],
        render: fn assigns ->
          status_class = if assigns.value == 1, do: "badge-success", else: "badge-error"
          status_text = if assigns.value == 1, do: "启用", else: "禁用"

          ~H"""
          <span class={"badge #{status_class}"}>{status_text}</span>
          """
        end
      },
      verified_at: %{
        module: Backpex.Fields.DateTime,
        label: "验证时间",
        help_text: "银行卡验证通过的时间"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "绑定时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Filters.StatusSelect
      },
      is_default: %{
        module: Teen.Filters.BooleanSelect
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      verify_cards: %{
        module: Teen.ResourceActions.VerifyBankCards,
        label: "验证银行卡",
        icon: "hero-check-badge",
        confirm_label: "确认验证",
        confirm_text: "确定要验证选中的银行卡吗？",
        fields: []
      },
      enable_cards: %{
        module: Teen.ResourceActions.EnableBankCards,
        label: "启用银行卡",
        icon: "hero-check-circle",
        confirm_label: "确认启用",
        confirm_text: "确定要启用选中的银行卡吗？",
        fields: []
      },
      disable_cards: %{
        module: Teen.ResourceActions.DisableBankCards,
        label: "禁用银行卡",
        icon: "hero-x-circle",
        confirm_label: "确认禁用",
        confirm_text: "确定要禁用选中的银行卡吗？",
        fields: []
      }
    ]
  end

  def item_actions do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      }
    ]
  end

  def render_resource_slot(assigns, :index_header) do
    ~H"""
    <div class="bg-base-100 p-6 rounded-lg shadow-sm mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-base-content">用户银行卡管理</h2>
          <p class="text-base-content/70 mt-1">管理用户绑定的银行卡信息</p>
        </div>
        <div class="stats shadow">
          <div class="stat">
            <div class="stat-title">总银行卡数</div>
            <div class="stat-value text-primary">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">已验证</div>
            <div class="stat-value text-success">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">待验证</div>
            <div class="stat-value text-warning">0</div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :index_footer) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg shadow-sm mt-6">
      <div class="text-sm text-base-content/70">
        <p><strong>操作说明：</strong></p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>银行卡号在列表中会进行脱敏显示</li>
          <li>每个用户只能有一张默认银行卡</li>
          <li>只有验证通过的银行卡才能用于提现</li>
          <li>禁用的银行卡不能进行任何操作</li>
        </ul>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :form_footer) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg shadow-sm mt-6">
      <div class="text-sm text-base-content/70">
        <p><strong>安全提醒：</strong></p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>银行卡信息属于敏感数据，请谨慎操作</li>
          <li>修改银行卡信息需要重新验证</li>
          <li>账户姓名必须与银行卡开户姓名一致</li>
        </ul>
      </div>
    </div>
    """
  end

  def render_resource_slot(_assigns, _slot), do: nil
end
