defmodule Teen.Protocol.MoneyProtocol do
  @moduledoc """
  金钱钱包协议处理器

  处理用户金钱、积分、钱包相关的协议，包括：
  - 获取余额信息
  - 充值记录查询
  - 提现申请
  - 转账操作
  - 交易记录
  - 钱包管理
  """

  @behaviour Teen.Protocol.ProtocolBehaviour

  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias <PERSON><PERSON>ridina.{Accounts, Ledger}
  alias Teen.PaymentSystem.{RechargeRecord, WithdrawalRecord}

  # 主协议ID
  @protocol_id 7

  # 子协议常量定义
  @cs_get_balance_p 0
  @sc_get_balance_p 1
  @cs_get_recharge_history_p 2
  @sc_get_recharge_history_p 3
  @cs_get_withdrawal_history_p 4
  @sc_get_withdrawal_history_p 5
  @cs_request_withdrawal_p 6
  @sc_request_withdrawal_p 7
  @cs_transfer_money_p 8
  @sc_transfer_money_p 9
  @cs_get_transaction_history_p 10
  @sc_get_transaction_history_p 11
  @cs_get_wallet_info_p 12
  @sc_get_wallet_info_p 13
  @cs_exchange_points_p 14
  @sc_exchange_points_p 15
  @cs_get_daily_statistics_p 16
  @sc_get_daily_statistics_p 17
  # CS_PAY: 请求支付
  @cs_pay_p 49
  @sc_pay_p 50
  # CS_WITHDRAWAL_EXCHANGE: 提现兑换
  @cs_withdrawal_exchange_p 29
  @sc_withdrawal_exchange_p 30

  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================

  @impl true
  def protocol_id, do: @protocol_id

  @impl true
  def protocol_info do
    %{
      name: "Money",
      description: "金钱钱包协议处理器"
    }
  end

  @impl true
  def supported_sub_protocols do
    [
      {@cs_get_balance_p, "获取余额信息"},
      {@cs_get_recharge_history_p, "获取充值记录"},
      {@cs_get_withdrawal_history_p, "获取提现记录"},
      {@cs_request_withdrawal_p, "申请提现"},
      {@cs_transfer_money_p, "转账操作"},
      {@cs_get_transaction_history_p, "获取交易记录"},
      {@cs_get_wallet_info_p, "获取钱包信息"},
      {@cs_exchange_points_p, "积分兑换"},
      {@cs_get_daily_statistics_p, "获取每日统计"},
      {@cs_pay_p, "请求支付"},
      {@cs_withdrawal_exchange_p, "提现兑换"}
    ]
  end

  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id

    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "MONEY", sub_protocol, user_id, "开始处理")

    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "MONEY", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end

  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_get_recharge_history_p ->
        ProtocolUtils.validate_params(data, [
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_get_withdrawal_history_p ->
        ProtocolUtils.validate_params(data, [
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_request_withdrawal_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:amount, :payment_method]},
          {:number_range, :amount, 1, 1_000_000, "提现金额"}
        ])

      @cs_transfer_money_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:to_user_id, :amount]},
          {:number_range, :amount, 1, 1_000_000, "转账金额"}
        ])

      @cs_get_transaction_history_p ->
        ProtocolUtils.validate_params(data, [
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_exchange_points_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:points]},
          {:number_range, :points, 1, 1_000_000, "兑换积分"}
        ])

      @cs_pay_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:amount]},
          {:number_range, :amount, 1, 1_000_000, "支付金额"}
        ])

      @cs_withdrawal_exchange_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:amount, :bank_id]},
          {:number_range, :amount, 1, 1_000_000, "兑换金额"}
        ])

      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    user_id = context.user_id

    case sub_protocol do
      @cs_get_balance_p ->
        handle_get_balance(user_id)

      @cs_get_recharge_history_p ->
        handle_get_recharge_history(user_id, data)

      @cs_get_withdrawal_history_p ->
        handle_get_withdrawal_history(user_id, data)

      @cs_request_withdrawal_p ->
        handle_request_withdrawal(user_id, data)

      @cs_transfer_money_p ->
        handle_transfer_money(user_id, data)

      @cs_get_transaction_history_p ->
        handle_get_transaction_history(user_id, data)

      @cs_get_wallet_info_p ->
        handle_get_wallet_info(user_id)

      @cs_exchange_points_p ->
        handle_exchange_points(user_id, data)

      @cs_get_daily_statistics_p ->
        handle_get_daily_statistics(user_id)

      @cs_pay_p ->
        handle_pay_request(user_id, data)

      @cs_withdrawal_exchange_p ->
        handle_withdrawal_exchange(user_id, data)

      _ ->
        ProtocolUtils.log_protocol(:warning, "MONEY", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end

  # 处理获取余额信息
  defp handle_get_balance(user_id) do
    try do
      points = Accounts.get_user_points(user_id)
      response_data =
        ProtocolUtils.success_response(%{
          "balance" => points,
          "points" => points,
          "frozenBalance" => 0,
          "totalRecharge" => 0,
          "totalWithdrawal" => 0,
          "lastUpdateTime" => ProtocolUtils.current_timestamp()
        })

      ProtocolUtils.log_protocol(:info, "MONEY", @cs_get_balance_p, user_id, "获取余额信息成功")
      {:ok, @sc_get_balance_p, response_data}
    rescue
      e ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_balance_p,
          user_id,
          "获取余额信息失败: #{inspect(e)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取余额信息失败")
        {:ok, @sc_get_balance_p, error_data}
    end
  end

  # 处理获取充值记录
  defp handle_get_recharge_history(user_id, data) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)

    case RechargeRecord.list_user_records(user_id, page, page_size) do
      {:ok, {records, total_count}} ->
        formatted_records = Enum.map(records, &format_recharge_record/1)

        response_data =
          ProtocolUtils.success_response(
            ProtocolUtils.build_pagination_response(
              formatted_records,
              page,
              page_size,
              total_count
            )
          )

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_get_recharge_history_p,
          user_id,
          "获取充值记录成功，共#{total_count}条"
        )

        {:ok, @sc_get_recharge_history_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_recharge_history_p,
          user_id,
          "获取充值记录失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取充值记录失败")
        {:ok, @sc_get_recharge_history_p, error_data}
    end
  end

  # 处理获取提现记录
  defp handle_get_withdrawal_history(user_id, data) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)

    case WithdrawalRecord.list_user_records(user_id, page, page_size) do
      {:ok, {records, total_count}} ->
        formatted_records = Enum.map(records, &format_withdrawal_record/1)

        response_data =
          ProtocolUtils.success_response(
            ProtocolUtils.build_pagination_response(
              formatted_records,
              page,
              page_size,
              total_count
            )
          )

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_get_withdrawal_history_p,
          user_id,
          "获取提现记录成功，共#{total_count}条"
        )

        {:ok, @sc_get_withdrawal_history_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_withdrawal_history_p,
          user_id,
          "获取提现记录失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取提现记录失败")
        {:ok, @sc_get_withdrawal_history_p, error_data}
    end
  end

  # 处理申请提现
  defp handle_request_withdrawal(user_id, data) do
    amount = Map.get(data, "amount")
    payment_method = Map.get(data, "payment_method")
    payment_info = Map.get(data, "payment_info", %{})

    withdrawal_params = %{
      amount: amount,
      payment_method: payment_method,
      bank_info: Map.get(payment_info, "bank_info"),
      alipay_info: Map.get(payment_info, "alipay_info"),
      upi_info: Map.get(payment_info, "upi_info")
    }

    case WithdrawalRecord.create_withdrawal_request(user_id, withdrawal_params) do
      {:ok, withdrawal} ->
        response_data =
          ProtocolUtils.success_response(%{
            "orderId" => withdrawal.order_id,
            "amount" => ProtocolUtils.format_money(withdrawal.amount),
            "status" => withdrawal.status,
            "msg" => "提现申请已提交，请等待审核"
          })

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_request_withdrawal_p,
          user_id,
          "提现申请成功: #{withdrawal.order_id}"
        )

        {:ok, @sc_request_withdrawal_p, response_data}

      {:error, :insufficient_balance} ->
        error_data = ProtocolUtils.error_response(:insufficient_balance, "余额不足")
        {:ok, @sc_request_withdrawal_p, error_data}

      {:error, :daily_limit_exceeded} ->
        error_data = ProtocolUtils.error_response(:forbidden, "已超过每日提现限额")
        {:ok, @sc_request_withdrawal_p, error_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_request_withdrawal_p,
          user_id,
          "提现申请失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "提现申请失败")
        {:ok, @sc_request_withdrawal_p, error_data}
    end
  end

  # 处理转账操作
  defp handle_transfer_money(user_id, data) do
    to_user_id = Map.get(data, "to_user_id")
    amount = Map.get(data, "amount")
    note = Map.get(data, "note", "")

    case Ledger.transfer_funds(user_id, to_user_id, amount, note) do
      {:ok, transfer} ->
        response_data =
          ProtocolUtils.success_response(%{
            "transferId" => transfer.id,
            "amount" => ProtocolUtils.format_money(amount),
            "toUserId" => to_user_id,
            "msg" => "转账成功"
          })

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_transfer_money_p,
          user_id,
          "转账成功: #{amount} -> #{to_user_id}"
        )

        {:ok, @sc_transfer_money_p, response_data}

      {:error, :insufficient_balance} ->
        error_data = ProtocolUtils.error_response(:insufficient_balance, "余额不足")
        {:ok, @sc_transfer_money_p, error_data}

      {:error, :user_not_found} ->
        error_data = ProtocolUtils.error_response(:not_found, "收款用户不存在")
        {:ok, @sc_transfer_money_p, error_data}

      {:error, :self_transfer} ->
        error_data = ProtocolUtils.error_response(:forbidden, "不能向自己转账")
        {:ok, @sc_transfer_money_p, error_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_transfer_money_p,
          user_id,
          "转账失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "转账失败")
        {:ok, @sc_transfer_money_p, error_data}
    end
  end

  # 处理获取交易记录
  defp handle_get_transaction_history(user_id, data) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)
    transaction_type = Map.get(data, "type", "all")

    case Ledger.get_user_transactions(user_id, transaction_type, page, page_size) do
      {:ok, {transactions, total_count}} ->
        formatted_transactions = Enum.map(transactions, &format_transaction/1)

        response_data =
          ProtocolUtils.success_response(
            ProtocolUtils.build_pagination_response(
              formatted_transactions,
              page,
              page_size,
              total_count
            )
          )

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_get_transaction_history_p,
          user_id,
          "获取交易记录成功，共#{total_count}条"
        )

        {:ok, @sc_get_transaction_history_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_transaction_history_p,
          user_id,
          "获取交易记录失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取交易记录失败")
        {:ok, @sc_get_transaction_history_p, error_data}
    end
  end

  # 处理获取钱包信息
  defp handle_get_wallet_info(user_id) do
    case Accounts.get_wallet_info(user_id) do
      {:ok, wallet_info} ->
        response_data =
          ProtocolUtils.success_response(%{
            "walletId" => wallet_info.wallet_id,
            "balance" => wallet_info.balance,
            "points" => wallet_info.points,
            "frozenBalance" => wallet_info.frozen_balance || 0,
            "creditLimit" => wallet_info.credit_limit || 0,
            "walletStatus" => wallet_info.status,
            "createdAt" => format_datetime(wallet_info.created_at),
            "lastTransactionAt" => format_datetime(wallet_info.last_transaction_at)
          })

        ProtocolUtils.log_protocol(:info, "MONEY", @cs_get_wallet_info_p, user_id, "获取钱包信息成功")
        {:ok, @sc_get_wallet_info_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_wallet_info_p,
          user_id,
          "获取钱包信息失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取钱包信息失败")
        {:ok, @sc_get_wallet_info_p, error_data}
    end
  end

  # 处理积分兑换
  defp handle_exchange_points(user_id, data) do
    points = Map.get(data, "points")
    # 100积分 = 1元
    exchange_rate = 1

    case Accounts.exchange_points_to_balance(user_id, points, exchange_rate) do
      {:ok, exchange_result} ->
        response_data =
          ProtocolUtils.success_response(%{
            "exchangedPoints" => points,
            "receivedBalance" => ProtocolUtils.format_money(exchange_result.balance_received),
            "exchangeRate" => exchange_rate,
            "remainingPoints" => exchange_result.remaining_points,
            "msg" => "积分兑换成功"
          })

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_exchange_points_p,
          user_id,
          "积分兑换成功: #{points}积分"
        )

        {:ok, @sc_exchange_points_p, response_data}

      {:error, :insufficient_points} ->
        error_data = ProtocolUtils.error_response(:insufficient_balance, "积分不足")
        {:ok, @sc_exchange_points_p, error_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_exchange_points_p,
          user_id,
          "积分兑换失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "积分兑换失败")
        {:ok, @sc_exchange_points_p, error_data}
    end
  end

  # 处理获取每日统计
  defp handle_get_daily_statistics(user_id) do
    case Accounts.get_daily_money_statistics(user_id) do
      {:ok, stats} ->
        response_data =
          ProtocolUtils.success_response(%{
            "todayRecharge" => ProtocolUtils.format_money(stats.today_recharge),
            "todayWithdrawal" => ProtocolUtils.format_money(stats.today_withdrawal),
            "todayGameWin" => ProtocolUtils.format_money(stats.today_game_win),
            "todayGameLoss" => ProtocolUtils.format_money(stats.today_game_loss),
            "weeklyRecharge" => ProtocolUtils.format_money(stats.weekly_recharge),
            "weeklyWithdrawal" => ProtocolUtils.format_money(stats.weekly_withdrawal),
            "monthlyRecharge" => ProtocolUtils.format_money(stats.monthly_recharge),
            "monthlyWithdrawal" => ProtocolUtils.format_money(stats.monthly_withdrawal)
          })

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_get_daily_statistics_p,
          user_id,
          "获取每日统计成功"
        )

        {:ok, @sc_get_daily_statistics_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_daily_statistics_p,
          user_id,
          "获取每日统计失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取每日统计失败")
        {:ok, @sc_get_daily_statistics_p, error_data}
    end
  end

  # 处理支付请求
  defp handle_pay_request(user_id, data) do
    Logger.info("💰 [MONEY_PAY] 收到支付请求: #{inspect(data)}")

    # 获取支付参数
    amount = Map.get(data, "amount", 0)
    pay_type = Map.get(data, "paytype", "alipay") |> to_string()
    charge_id = Map.get(data, "chargeid", "") |> to_string()

    # 验证支付参数
    cond do
      amount <= 0 ->
        error_data = ProtocolUtils.error_response(:invalid_params, "支付金额无效")
        {:ok, @sc_pay_p, error_data}

      String.length(charge_id) == 0 ->
        error_data = ProtocolUtils.error_response(:invalid_params, "充值配置ID无效")
        {:ok, @sc_pay_p, error_data}

      true ->
        # 调用支付系统创建订单
        case Teen.PaymentSystem.PaymentService.create_order(%{
               user_id: user_id,
               amount: Decimal.new(amount),
               currency: "INR",
               channel_id: map_pay_type_to_channel_id(pay_type),
               notify_url: get_payment_notify_url(),
               return_url: get_payment_return_url()
             }) do
          {:ok, payment_result} ->
            response_data =
              ProtocolUtils.success_response(%{
                "orderid" => payment_result.order_id,
                "msg" => payment_result.payment_url,
                "payurl" => payment_result.payment_url,
                "amount" => amount,
                "paytype" => pay_type,
                "status" => payment_result.status
              })

            ProtocolUtils.log_protocol(
              :info,
              "MONEY",
              @cs_pay_p,
              user_id,
              "支付订单创建成功: #{payment_result.order_id}"
            )

            {:ok, @sc_pay_p, response_data}

          {:error, reason} ->
            ProtocolUtils.log_protocol(
              :error,
              "MONEY",
              @cs_pay_p,
              user_id,
              "创建支付订单失败: #{inspect(reason)}"
            )

            error_data = ProtocolUtils.error_response(:internal_error, "支付订单创建失败")
            {:ok, @sc_pay_p, error_data}
        end
    end
  end

  # ============================================================================
  # 工具函数
  # ============================================================================

  defp format_recharge_record(record) do
    %{
      "orderId" => record.order_id,
      "amount" => ProtocolUtils.format_money(record.amount),
      "paymentMethod" => record.payment_method,
      "status" => record.status,
      "createdAt" => format_datetime(record.inserted_at),
      "completedAt" => format_datetime(record.completed_at)
    }
  end

  defp format_withdrawal_record(record) do
    %{
      "orderId" => record.order_id,
      "amount" => ProtocolUtils.format_money(record.withdrawal_amount),
      "actualAmount" => ProtocolUtils.format_money(record.actual_amount),
      "feeAmount" => ProtocolUtils.format_money(record.fee_amount),
      "paymentMethod" => record.payment_method,
      "auditStatus" => record.audit_status,
      "progressStatus" => record.progress_status,
      "resultStatus" => record.result_status,
      "createdAt" => format_datetime(record.inserted_at),
      "auditTime" => format_datetime(record.audit_time),
      "processTime" => format_datetime(record.process_time),
      "completedTime" => format_datetime(record.completed_time),
      "feedback" => record.feedback
    }
  end

  defp format_transaction(transaction) do
    %{
      "id" => transaction.id,
      "type" => transaction.type,
      "amount" => ProtocolUtils.format_money(transaction.amount),
      "description" => transaction.description,
      "createdAt" => format_datetime(transaction.inserted_at),
      "balanceAfter" => ProtocolUtils.format_money(transaction.balance_after)
    }
  end

  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_unix(datetime, :millisecond)

  # 获取支付通知URL
  defp get_payment_notify_url do
    base_url = Application.get_env(:cypridina, :base_url, "http://localhost:4000")
    "#{base_url}/api/payment/notify"
  end

  # 获取支付返回URL
  defp get_payment_return_url do
    base_url = Application.get_env(:cypridina, :base_url, "http://localhost:4000")
    "#{base_url}/api/payment/return"
  end

  # 将支付类型映射为渠道ID
  defp map_pay_type_to_channel_id(pay_type) do
    case pay_type do
      "alipay" -> "3021"
      "wechat" -> "3021"
      "unionpay" -> "3021"
      "ebank" -> "3021"
      # 处理数字类型的paytype（客户端传递的遗留格式）
      # 支付宝
      "1" -> "3021"
      # 微信
      "2" -> "3021"
      # 银联
      "3" -> "3021"
      # 网银
      "4" -> "3021"
      # 第三方支付
      "5" -> "3021"
      # 默认使用第三方支付
      _ -> "3021"
    end
  end

  # 处理提现兑换
  defp handle_withdrawal_exchange(user_id, data) do
    Logger.info("💰 [MONEY_WITHDRAWAL_EXCHANGE] 用户:#{user_id} 开始处理提现兑换")
    
    # 获取请求参数
    amount = Map.get(data, "amount")
    bank_id = Map.get(data, "bank_id")
    bank_account_name = Map.get(data, "bank_account_name", "")
    bank_account_number = Map.get(data, "bank_account_number", "")
    bank_ifsc = Map.get(data, "bank_ifsc", "")
    
    # 构建提现兑换参数
    withdrawal_params = %{
      amount: Decimal.new(amount),
      bank_id: bank_id,
      bank_account_name: bank_account_name,
      bank_account_number: bank_account_number,
      bank_ifsc: bank_ifsc,
      payment_method: "bank_transfer",
      exchange_type: "withdrawal"
    }
    
    try do
      # 开始数据库事务
      Cypridina.Repo.transaction(fn ->
        # 1. 验证用户余额
        try do
          points = Accounts.get_user_points(user_id)
          if Decimal.compare(points, withdrawal_params.amount) == :lt do
            Cypridina.Repo.rollback({:error, :insufficient_balance})
          end
          
          # 2. 验证银行配置
          case Teen.PaymentSystem.BankConfig.get_by_code(bank_id) do
            {:ok, bank_config} ->
              if bank_config.status != 1 do
                Cypridina.Repo.rollback({:error, :bank_not_available})
              end
              
              # 3. 验证提现限额
              if Decimal.compare(withdrawal_params.amount, bank_config.min_amount) == :lt do
                Cypridina.Repo.rollback({:error, :amount_too_small})
              end
              
              if Decimal.compare(withdrawal_params.amount, bank_config.max_amount) == :gt do
                Cypridina.Repo.rollback({:error, :amount_too_large})
              end
              
              # 4. 计算手续费
              fee_amount = Decimal.mult(withdrawal_params.amount, Decimal.div(bank_config.fee_rate, 100))
              final_amount = Decimal.sub(withdrawal_params.amount, fee_amount)
              
              # 5. 创建提现记录
              withdrawal_record_params = %{
                user_id: user_id,
                amount: withdrawal_params.amount,
                final_amount: final_amount,
                fee_amount: fee_amount,
                bank_id: bank_id,
                bank_name: bank_config.name,
                bank_account_name: bank_account_name,
                bank_account_number: bank_account_number,
                bank_ifsc: bank_ifsc,
                payment_method: "bank_transfer",
                status: 0,  # 待审核
                order_id: generate_order_id()
              }
              
              case WithdrawalRecord.create(withdrawal_record_params) do
                {:ok, withdrawal_record} ->
                  # 6. 冻结用户余额（通过扣除积分实现）
                  case Accounts.subtract_points(user_id, withdrawal_params.amount, [
                    transaction_type: :withdrawal_freeze,
                    description: "提现兑换冻结",
                    metadata: %{
                      withdrawal_id: withdrawal_record.id,
                      bank_id: bank_id,
                      bank_name: bank_config.name,
                      fee_amount: fee_amount
                    }
                  ]) do
                    {:ok, _} ->
                      # 7. 记录交易日志已通过subtract_points自动完成
                      
                      # 返回成功结果
                      {withdrawal_record, bank_config, fee_amount, final_amount}
                      
                    {:error, reason} ->
                      Cypridina.Repo.rollback({:error, reason})
                  end
                  
                {:error, reason} ->
                  Cypridina.Repo.rollback({:error, reason})
              end
              
            {:error, reason} ->
              Cypridina.Repo.rollback({:error, reason})
          end
        rescue
          e ->
            Cypridina.Repo.rollback({:error, e})
        end
      end)
      |> case do
        {:ok, {withdrawal_record, bank_config, fee_amount, final_amount}} ->
          # 构建成功响应
          response_data = ProtocolUtils.success_response(%{
            "orderId" => withdrawal_record.order_id,
            "amount" => ProtocolUtils.format_money(withdrawal_record.amount),
            "finalAmount" => ProtocolUtils.format_money(final_amount),
            "feeAmount" => ProtocolUtils.format_money(fee_amount),
            "bankName" => bank_config.name,
            "bankAccountName" => bank_account_name,
            "bankAccountNumber" => bank_account_number,
            "bankIfsc" => bank_ifsc,
            "status" => withdrawal_record.status,
            "createdAt" => ProtocolUtils.current_timestamp(),
            "msg" => "提现兑换申请已提交，请等待审核"
          })
          
          ProtocolUtils.log_protocol(
            :info,
            "MONEY",
            @cs_withdrawal_exchange_p,
            user_id,
            "提现兑换申请成功: #{withdrawal_record.order_id}, 金额: #{withdrawal_record.amount}"
          )
          
          {:ok, @sc_withdrawal_exchange_p, response_data}
          
        {:error, {:error, :insufficient_balance}} ->
          error_data = ProtocolUtils.error_response(:insufficient_balance, "余额不足")
          {:ok, @sc_withdrawal_exchange_p, error_data}
          
        {:error, {:error, :bank_not_available}} ->
          error_data = ProtocolUtils.error_response(:forbidden, "银行不可用")
          {:ok, @sc_withdrawal_exchange_p, error_data}
          
        {:error, {:error, :amount_too_small}} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "提现金额低于最小限额")
          {:ok, @sc_withdrawal_exchange_p, error_data}
          
        {:error, {:error, :amount_too_large}} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "提现金额超过最大限额")
          {:ok, @sc_withdrawal_exchange_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "MONEY",
            @cs_withdrawal_exchange_p,
            user_id,
            "提现兑换申请失败: #{inspect(reason)}"
          )
          
          error_data = ProtocolUtils.error_response(:internal_error, "提现兑换申请失败")
          {:ok, @sc_withdrawal_exchange_p, error_data}
      end
      
    rescue
      e ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_withdrawal_exchange_p,
          user_id,
          "提现兑换处理异常: #{inspect(e)}"
        )
        
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常")
        {:ok, @sc_withdrawal_exchange_p, error_data}
    end
  end
  
  # 生成唯一订单ID
  defp generate_order_id do
    timestamp = DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    random_part = :crypto.strong_rand_bytes(4) |> Base.encode16(case: :lower)
    "WE#{timestamp}#{random_part}"
  end
end
