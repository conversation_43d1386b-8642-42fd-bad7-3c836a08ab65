defmodule Teen.Protocol.RegLoginProtocol do
  @moduledoc """
  注册登录协议处理器
  
  处理用户注册、登录、登出等相关协议
  """
  
  @behaviour Teen.Protocol.ProtocolBehaviour
  
  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.{User, LoginService}
  alias Teen.PaymentSystem
  
  # 主协议ID
  @protocol_id 0
  
  # 子协议常量定义
  @cs_normal_reg_p 0
  @sc_normal_reg_p 1
  @cs_login_p 2
  @sc_login_p 3
  @cs_login_out_p 4
  @sc_other_login_p 10
  @sc_login_other_p 11
  @sc_server_stop_p 12
  @sc_fullconnect_attack_p 14
  @cs_heart_check_p 19
  @cs_request_reg_phonecode_p 29
  @sc_request_reg_phonecode_p 30
  @cs_phonecode_reg_p 31
  @sc_phonecode_reg_p 32
  # CS_REQUEST_SERVER_VERSION_P: 请求版本号
  @cs_request_server_version_p 38
  # CD_REQUEST_SYSTEM_STATUS_P: 请求系统配置
  @cs_request_system_status_p 49
  @sc_request_system_status_p 50
  # CS_REQUEST_GAMEVERSIONS_P: 请求游戏版本号列表
  @cs_request_gameversions_p 51
  @sc_request_gameversions_p 52
  
  # 登录结果码
  @login_success 0
  @login_success_leave 1
  @login_forbid 2
  @login_no_account 3
  @login_psw_error 4
  @login_version_error 10
  
  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================
  
  @impl true
  def protocol_id, do: @protocol_id
  
  @impl true
  def protocol_info do
    %{
      name: "RegLogin",
      description: "注册登录协议处理器"
    }
  end
  
  @impl true
  def supported_sub_protocols do
    [
      {@cs_normal_reg_p, "普通注册"},
      {@cs_login_p, "用户登录"},
      {@cs_login_out_p, "用户登出"},
      {@cs_heart_check_p, "心跳检查"},
      {@cs_request_reg_phonecode_p, "请求手机验证码"},
      {@cs_phonecode_reg_p, "手机注册"},
      {@cs_request_server_version_p, "请求服务器版本号"},
      {@cs_request_system_status_p, "请求系统状态"},
      {@cs_request_gameversions_p, "请求游戏版本列表"}
    ]
  end
  
  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id || "anonymous"
    
    # 记录协议处理开始
    ProtocolUtils.log_protocol(:debug, "REG_LOGIN", sub_protocol, user_id, "开始处理")
    
    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "REG_LOGIN", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end
  
  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_normal_reg_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:username, :password]},
          {:string_length, :username, 3, 20, "用户名"},
          {:string_length, :password, 6, 20, "密码"}
        ])
        
      @cs_login_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:username, :password]}
        ])
        
      @cs_phonecode_reg_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:phone, :code, :password]},
          {:string_length, :phone, 10, 15, "手机号"},
          {:string_length, :code, 4, 6, "验证码"},
          {:string_length, :password, 6, 20, "密码"}
        ])
        
      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end
  
  # ============================================================================
  # 私有函数
  # ============================================================================
  
  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    case sub_protocol do
      @cs_normal_reg_p ->
        handle_normal_register(data, context)
        
      @cs_login_p ->
        handle_login(data, context)
        
      @cs_login_out_p ->
        handle_logout(context)
        
      @cs_heart_check_p ->
        handle_heart_check(context)
        
      @cs_request_reg_phonecode_p ->
        handle_request_phone_code(data, context)
        
      @cs_phonecode_reg_p ->
        handle_phone_register(data, context)
        
      @cs_request_server_version_p ->
        handle_request_server_version(data, context)
        
      @cs_request_system_status_p ->
        handle_request_system_status(data, context)
        
      @cs_request_gameversions_p ->
        handle_request_game_versions(data, context)
        
      _ ->
        {:error, :unknown_sub_protocol}
    end
  end
  
  # 处理普通注册
  defp handle_normal_register(data, context) do
    username = Map.get(data, "username")
    password = Map.get(data, "password")
    
    case LoginService.register_user(username, password) do
      {:ok, user} ->
        response_data = ProtocolUtils.success_response(%{
          "userid" => user.id,
          "username" => user.username,
          "msg" => "注册成功"
        })
        
        ProtocolUtils.log_protocol(:info, "REG_LOGIN", @cs_normal_reg_p, user.id, "注册成功")
        {:ok, @sc_normal_reg_p, response_data}
        
      {:error, :user_exists} ->
        error_data = ProtocolUtils.error_response(:already_exists, "用户名已存在")
        {:ok, @sc_normal_reg_p, error_data}
        
      {:error, reason} ->
        error_data = ProtocolUtils.error_response(:internal_error, "注册失败: #{inspect(reason)}")
        {:ok, @sc_normal_reg_p, error_data}
    end
  end
  
  # 处理用户登录
  defp handle_login(data, context) do
    username = Map.get(data, "username")
    password = Map.get(data, "password")
    session_id = context.session_id
    
    case LoginService.authenticate_user(username, password) do
      {:ok, user} ->
        # 更新session信息
        LoginService.update_session(session_id, user.id)
        
        response_data = %{
          "loginResult" => @login_success,
          "userid" => user.id,
          "username" => user.username,
          "points" => user.points || 0,
          "vipLevel" => user.vip_level || 0
        }
        
        ProtocolUtils.log_protocol(:info, "REG_LOGIN", @cs_login_p, user.id, "登录成功")
        {:ok, @sc_login_p, response_data}
        
      {:error, :user_not_found} ->
        response_data = %{
          "loginResult" => @login_no_account,
          "msg" => "账号不存在"
        }
        {:ok, @sc_login_p, response_data}
        
      {:error, :invalid_password} ->
        response_data = %{
          "loginResult" => @login_psw_error,
          "msg" => "密码错误"
        }
        {:ok, @sc_login_p, response_data}
        
      {:error, :user_banned} ->
        response_data = %{
          "loginResult" => @login_forbid,
          "msg" => "账号已被封禁"
        }
        {:ok, @sc_login_p, response_data}
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "REG_LOGIN", @cs_login_p, "unknown", "登录失败: #{inspect(reason)}")
        response_data = %{
          "loginResult" => @login_psw_error,
          "msg" => "登录失败"
        }
        {:ok, @sc_login_p, response_data}
    end
  end
  
  # 处理用户登出
  defp handle_logout(context) do
    user_id = context.user_id
    session_id = context.session_id
    
    if user_id do
      LoginService.logout_user(session_id)
      ProtocolUtils.log_protocol(:info, "REG_LOGIN", @cs_login_out_p, user_id, "用户登出")
    end
    
    # 登出不需要返回响应
    {:noreply}
  end
  
  # 处理心跳检查
  defp handle_heart_check(context) do
    user_id = context.user_id || "anonymous"
    ProtocolUtils.log_protocol(:debug, "REG_LOGIN", @cs_heart_check_p, user_id, "心跳检查")
    
    # 心跳不需要返回响应
    {:noreply}
  end
  
  # 处理请求手机验证码
  defp handle_request_phone_code(data, context) do
    phone = Map.get(data, "phone")
    
    case LoginService.send_phone_verification_code(phone) do
      {:ok, _} ->
        response_data = ProtocolUtils.success_response(%{
          "msg" => "验证码已发送"
        })
        {:ok, @sc_request_reg_phonecode_p, response_data}
        
      {:error, :rate_limited} ->
        error_data = ProtocolUtils.error_response(:rate_limited, "请求过于频繁，请稍后再试")
        {:ok, @sc_request_reg_phonecode_p, error_data}
        
      {:error, reason} ->
        error_data = ProtocolUtils.error_response(:internal_error, "发送验证码失败")
        ProtocolUtils.log_protocol(:error, "REG_LOGIN", @cs_request_reg_phonecode_p, 
          context.user_id || "anonymous", "发送验证码失败: #{inspect(reason)}")
        {:ok, @sc_request_reg_phonecode_p, error_data}
    end
  end
  
  # 处理手机注册
  defp handle_phone_register(data, context) do
    phone = Map.get(data, "phone")
    code = Map.get(data, "code")
    password = Map.get(data, "password")
    
    case LoginService.register_with_phone(phone, code, password) do
      {:ok, user} ->
        response_data = ProtocolUtils.success_response(%{
          "userid" => user.id,
          "username" => user.username,
          "msg" => "注册成功"
        })
        
        ProtocolUtils.log_protocol(:info, "REG_LOGIN", @cs_phonecode_reg_p, user.id, "手机注册成功")
        {:ok, @sc_phonecode_reg_p, response_data}
        
      {:error, :invalid_code} ->
        error_data = ProtocolUtils.error_response(:invalid_params, "验证码错误")
        {:ok, @sc_phonecode_reg_p, error_data}
        
      {:error, :phone_exists} ->
        error_data = ProtocolUtils.error_response(:already_exists, "手机号已注册")
        {:ok, @sc_phonecode_reg_p, error_data}
        
      {:error, reason} ->
        error_data = ProtocolUtils.error_response(:internal_error, "注册失败")
        ProtocolUtils.log_protocol(:error, "REG_LOGIN", @cs_phonecode_reg_p,
          context.user_id || "anonymous", "手机注册失败: #{inspect(reason)}")
        {:ok, @sc_phonecode_reg_p, error_data}
    end
  end

  # 处理请求服务器版本号
  defp handle_request_server_version(data, context) do
    user_id = context.user_id || "anonymous"
    
    ProtocolUtils.log_protocol(:info, "REG_LOGIN", @cs_request_server_version_p, user_id, "请求服务器版本号")
    
    response_data = ProtocolUtils.success_response(%{
      "hallVersion" => "1.0.5",
      "gameVersion" => "1.0.3",
      "clientVersion" => "1.0.1",
      "serverTime" => System.system_time(:millisecond)
    })
    
    {:ok, @cs_request_server_version_p, response_data}
  end

  # 处理请求系统状态
  defp handle_request_system_status(data, context) do
    user_id = context.user_id || "anonymous"
    site_id = Map.get(data, "siteid", 501)
    
    ProtocolUtils.log_protocol(:info, "REG_LOGIN", @cs_request_system_status_p, user_id, "请求系统状态")
    
    # 获取基础系统状态
    base_status = %{
      "systemStatus" => "running",
      "maintenance" => false,
      "registerEnabled" => true,
      "exchangeEnabled" => true,
      "chargeEnabled" => true,
      "serverLoad" => 0.65,
      "onlineUsers" => 1250,
      "siteId" => site_id,
      "serverTime" => System.system_time(:millisecond)
    }
    
    # 获取支付系统配置（包含RechargeConfig、PaymentConfig、BankConfig）
    payment_config = case PaymentSystem.get_system_config(user_id) do
      {:ok, config} -> config
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "REG_LOGIN", @cs_request_system_status_p, user_id, "获取支付配置失败: #{inspect(reason)}")
        %{}
    end
    
    # 合并基础状态和支付配置
    system_config = Map.merge(base_status, payment_config)
    
    response_data = ProtocolUtils.success_response(system_config)
    
    {:ok, @sc_request_system_status_p, response_data}
  end

  # 处理请求游戏版本列表
  defp handle_request_game_versions(data, context) do
    user_id = context.user_id || "anonymous"
    site_id = Map.get(data, "siteid", 501)
    
    ProtocolUtils.log_protocol(:info, "REG_LOGIN", @cs_request_gameversions_p, user_id, "请求游戏版本列表")
    
    game_versions = %{
      "1" => "1.0.1",    # Teen Patti
      "3" => "1.0.1",    # Pot Blind  
      "21" => "1.0.1",   # Jhandi Munda
      "22" => "1.0.1",   # Dragon Tiger
      "23" => "1.0.1",   # Crash
      "40" => "1.0.1",   # Slot777
      "41" => "1.0.1",   # SlotNiu
      "42" => "1.0.1",   # SlotCat
      "54" => "1.0.1"    # Safari of Wealth
    }
    
    response_data = ProtocolUtils.success_response(%{
      "gameVersions" => game_versions,
      "serverTime" => System.system_time(:millisecond),
      "siteId" => site_id
    })
    
    {:ok, @sc_request_gameversions_p, response_data}
  end
end