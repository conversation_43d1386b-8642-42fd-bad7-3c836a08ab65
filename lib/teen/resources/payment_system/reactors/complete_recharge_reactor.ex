defmodule Teen.PaymentSystem.Reactors.CompleteRechargeReactor do
  @moduledoc """
  Ash.Reactor 工作流用于处理充值完成的完整业务流程
  
  包含以下步骤：
  1. 更新充值记录状态 
  2. 获取充值前余额
  3. 向用户账户添加金币
  4. 验证余额变化
  5. 发布充值完成事件
  
  提供完整的错误处理和补偿机制
  """
  use Reactor

  require Logger

  # 定义输入参数
  input :record_id
  input :external_order_id
  input :callback_data

  # 步骤1: 更新充值记录状态
  step :update_record do
    argument :record_id, input(:record_id)
    argument :external_order_id, input(:external_order_id)
    argument :callback_data, input(:callback_data)
    
    run fn %{record_id: record_id, external_order_id: external_order_id, callback_data: callback_data}, _context ->
      Logger.info("💰 [REACTOR] 更新充值记录状态: #{record_id}")
      
      with {:ok, record} <- Teen.PaymentSystem.RechargeRecord.get_by_id(record_id),
           {:ok, updated_record} <- Teen.PaymentSystem.RechargeRecord.complete_recharge(record, %{
             external_order_id: external_order_id,
             callback_data: callback_data
           }) do
        Logger.info("💰 [REACTOR] 充值记录状态更新成功")
        {:ok, updated_record}
      else
        {:error, reason} ->
          Logger.error("💰 [REACTOR] 更新充值记录失败: #{inspect(reason)}")
          {:error, "Failed to update record: #{inspect(reason)}"}
      end
    end
  end

  # 步骤2: 获取充值前余额
  step :get_balance_before do
    argument :record, result(:update_record)
    
    run fn %{record: record}, _context ->
      Logger.info("💰 [REACTOR] 获取用户 #{record.user_id} 充值前余额")
      
      user_identifier = Cypridina.Ledger.AccountIdentifier.user(record.user_id, :XAA)
      
      case Cypridina.Ledger.BalanceCache.get_balance(user_identifier) do
        {:ok, balance} ->
          Logger.info("💰 [REACTOR] 充值前余额: #{balance}")
          {:ok, %{balance: balance, user_identifier: user_identifier}}
        
        {:error, reason} ->
          Logger.error("💰 [REACTOR] 获取余额失败: #{inspect(reason)}")
          {:error, "Failed to get balance: #{inspect(reason)}"}
      end
    end
  end

  # 步骤3: 添加金币到用户账户 (包含失败时的回滚处理)
  step :add_coins do
    argument :record, result(:update_record)
    argument :balance_info, result(:get_balance_before)
    
    run fn %{record: record, balance_info: _balance_info}, _context ->
      Logger.info("💰 [REACTOR] 开始为用户 #{record.user_id} 添加金币，金额: #{record.amount}")
      
      case add_coins_to_user(record.user_id, record.amount) do
        {:ok, transfer_result} ->
          Logger.info("💰 [REACTOR] 金币添加成功: #{inspect(transfer_result)}")
          {:ok, transfer_result}
        
        {:error, reason} ->
          Logger.error("💰 [REACTOR] 金币添加失败: #{inspect(reason)}")
          
          # 添加金币失败时，回滚充值记录状态
          Logger.warn("💰 [REACTOR] 执行补偿: 回滚充值记录状态")
          
          case Teen.PaymentSystem.RechargeRecord.fail_recharge(record, %{
            error_message: "充值处理失败，金币添加失败",
            callback_data: %{reactor_compensation: true, original_error: reason}
          }) do
            {:ok, _reverted_record} ->
              Logger.info("💰 [REACTOR] 补偿成功: 充值记录已回滚到失败状态")
            {:error, revert_reason} ->
              Logger.error("💰 [REACTOR] 补偿失败: #{inspect(revert_reason)}")
          end
          
          {:error, "Failed to add coins: #{inspect(reason)}"}
      end
    end
  end

  # 步骤4: 验证余额变化 (包含验证失败时的报警处理)
  step :validate_balance do
    argument :record, result(:update_record)
    argument :balance_info, result(:get_balance_before)
    argument :transfer_result, result(:add_coins)
    
    run fn %{record: record, balance_info: %{balance: balance_before, user_identifier: user_identifier}, transfer_result: _transfer_result}, _context ->
      Logger.info("💰 [REACTOR] 验证余额变化")
      
      case Cypridina.Ledger.BalanceCache.get_balance(user_identifier) do
        {:ok, balance_after} ->
          expected_amount = if is_struct(record.amount, Decimal) do
            Decimal.to_integer(record.amount)
          else
            record.amount
          end
          
          if balance_after == balance_before + expected_amount do
            Logger.info("💰 [REACTOR] 余额验证成功: #{balance_before} -> #{balance_after} (增加 #{expected_amount})")
            {:ok, %{validated: true, balance_before: balance_before, balance_after: balance_after}}
          else
            Logger.error("💰 [REACTOR] 余额验证失败: 期望增加 #{expected_amount}, 实际: #{balance_before} -> #{balance_after}")
            
            # 验证失败时的补偿处理 - 发送系统报警
            Logger.warn("💰 [REACTOR] 执行补偿: 发送系统报警")
            Logger.error("💰 [REACTOR] 关键警告: 金币已添加但验证失败，需要人工检查 user_id: #{record.user_id}, amount: #{record.amount}")
            
            # 发送报警通知
            try do
              Phoenix.PubSub.broadcast(
                Cypridina.PubSub,
                "system_alerts",
                %{
                  event: :recharge_validation_failed,
                  type: "critical",
                  user_id: record.user_id,
                  order_id: record.order_id,
                  amount: record.amount,
                  expected_balance: balance_before + expected_amount,
                  actual_balance: balance_after,
                  message: "金币添加成功但余额验证失败，需要人工处理",
                  timestamp: DateTime.utc_now()
                }
              )
              Logger.warn("💰 [REACTOR] 已发送系统报警: 充值验证失败")
            rescue
              error ->
                Logger.error("💰 [REACTOR] 发送报警失败: #{inspect(error)}")
            end
            
            {:error, "Balance validation failed: expected #{balance_before + expected_amount}, got #{balance_after}"}
          end
        
        {:error, reason} ->
          Logger.error("💰 [REACTOR] 获取验证余额失败: #{inspect(reason)}")
          {:error, "Failed to get balance for validation: #{inspect(reason)}"}
      end
    end
  end

  # 步骤5: 发布充值完成事件
  step :publish_event do
    argument :record, result(:update_record)
    argument :validation, result(:validate_balance)
    
    run fn %{record: record, validation: %{validated: true}}, _context ->
      Logger.info("💰 [REACTOR] 发布充值完成事件")
      
      try do
        publish_recharge_event(record)
        Logger.info("💰 [REACTOR] 事件发布成功")
        {:ok, %{event_published: true}}
      rescue
        error ->
          Logger.error("💰 [REACTOR] 事件发布失败: #{inspect(error)}")
          {:error, "Failed to publish event: #{inspect(error)}"}
      end
    end
  end

  # 返回最终结果
  step :final_result do
    argument :record, result(:update_record)
    argument :validation, result(:validate_balance)
    argument :event, result(:publish_event)
    
    run fn %{record: record, validation: validation, event: event}, _context ->
      Logger.info("💰 [REACTOR] 充值完成流程成功")
      
      {:ok, %{
        record: record,
        validation: validation,
        event: event,
        status: :completed
      }}
    end
  end


  # 私有辅助函数 - 复用 RechargeRecord 中的逻辑
  defp add_coins_to_user(user_id, amount) do
    system_identifier = Cypridina.Ledger.AccountIdentifier.system(:main, :XAA)
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)

    amount_integer = if is_struct(amount, Decimal) do
      Decimal.to_integer(amount)
    else
      amount
    end

    Logger.info("💰 [REACTOR:LEDGER] 准备转账: 从系统账户 -> 用户 #{user_id}, 金额: #{amount_integer}")

    case Cypridina.Ledger.transfer(
           system_identifier,
           user_identifier,
           amount_integer,
           transaction_type: :deposit,
           description: "用户充值"
         ) do
      {:ok, result} ->
        Logger.info("💰 [REACTOR:LEDGER] 转账成功: #{inspect(result)}")
        {:ok, result}

      {:error, reason} ->
        Logger.error("💰 [REACTOR:LEDGER] 转账失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp publish_recharge_event(record) do
    # 暂时使用简单的日志记录，稍后可以集成实际的事件发布
    Logger.info("💰 [REACTOR] 发布充值完成事件: user_id=#{record.user_id}, amount=#{record.amount}, order_id=#{record.order_id}")
    
    # 可以使用 PubSub 直接发布事件
    Phoenix.PubSub.broadcast(
      Cypridina.PubSub,
      "recharge_completed:#{record.user_id}",
      %{
        event: :recharge_completed,
        user_id: record.user_id,
        amount: record.amount,
        order_id: record.order_id,
        payment_method: record.payment_method,
        completed_at: record.completed_at
      }
    )
  end
end