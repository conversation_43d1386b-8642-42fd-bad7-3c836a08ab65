defmodule Teen.UserSystem.UserTurnover do
  @moduledoc """
  用户流水记录资源

  管理用户的流水洗码信息，包括：
  - 所需流水金额
  - 已完成流水金额
  - 流水类型和来源
  - 流水历史记录
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.UserSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :user_id, :required_turnover, :completed_turnover, :bonusmoney, :bonuscashmoney, :winningmoney, :status, :updated_at]
  end

  postgres do
    table "user_turnovers"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :get_by_user
    define :add_requirement
    define :update_completed
    define :reset_turnover
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :get_by_user do
      argument :user_id, :uuid, allow_nil?: false
      get? true
      filter expr(user_id == ^arg(:user_id))
    end

    create :create_for_user do
      accept [:user_id, :required_turnover, :completed_turnover]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, 1)
      end
    end

    update :add_requirement do
      argument :additional_amount, :decimal, allow_nil?: false
      argument :bonus_type, :string, allow_nil?: false

      change fn changeset, _context ->
        current_required =
          Ash.Changeset.get_attribute(changeset, :required_turnover) || Decimal.new("0")

        additional = Ash.Changeset.get_argument(changeset, :additional_amount)
        bonus_type = Ash.Changeset.get_argument(changeset, :bonus_type)

        # 根据奖励类型计算流水倍数
        multiplier = Teen.Services.TurnoverService.get_turnover_multiplier(bonus_type)
        required_turnover = Decimal.mult(additional, multiplier)

        new_required = Decimal.add(current_required, required_turnover)

        changeset
        |> Ash.Changeset.change_attribute(:required_turnover, new_required)
        |> Ash.Changeset.change_attribute(:last_bonus_type, bonus_type)
        |> Ash.Changeset.change_attribute(:last_bonus_amount, additional)
      end
    end

    update :update_completed do
      argument :bet_amount, :decimal, allow_nil?: false

      change fn changeset, _context ->
        current_completed =
          Ash.Changeset.get_attribute(changeset, :completed_turnover) || Decimal.new("0")

        bet_amount = Ash.Changeset.get_argument(changeset, :bet_amount)

        new_completed = Decimal.add(current_completed, bet_amount)

        changeset
        |> Ash.Changeset.change_attribute(:completed_turnover, new_completed)
        |> Ash.Changeset.change_attribute(:last_bet_amount, bet_amount)
        |> Ash.Changeset.change_attribute(:last_bet_time, DateTime.utc_now())
      end
    end

    update :reset_turnover do
      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:required_turnover, Decimal.new("0"))
        |> Ash.Changeset.change_attribute(:completed_turnover, Decimal.new("0"))
        |> Ash.Changeset.change_attribute(:reset_time, DateTime.utc_now())
        |> Ash.Changeset.change_attribute(:reset_reason, "余额低于5卢比自动清零")
      end
    end

    update :update_balance do
      accept [:bonusmoney, :bonuscashmoney, :winningmoney]
      
      argument :bonusmoney, :integer, allow_nil?: true
      argument :bonuscashmoney, :integer, allow_nil?: true
      argument :winningmoney, :integer, allow_nil?: true
      
      change fn changeset, _context ->
        changeset
        |> maybe_change_attribute(:bonusmoney, Ash.Changeset.get_argument(changeset, :bonusmoney))
        |> maybe_change_attribute(:bonuscashmoney, Ash.Changeset.get_argument(changeset, :bonuscashmoney))
        |> maybe_change_attribute(:winningmoney, Ash.Changeset.get_argument(changeset, :winningmoney))
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :required_turnover, :decimal do
      allow_nil? false
      public? true
      description "所需流水金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :completed_turnover, :decimal do
      allow_nil? false
      public? true
      description "已完成流水金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    # 最近操作记录
    attribute :last_bonus_type, :string do
      allow_nil? true
      public? true
      description "最近一次奖励类型"
      constraints max_length: 50
    end

    attribute :last_bonus_amount, :decimal do
      allow_nil? true
      public? true
      description "最近一次奖励金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :last_bet_amount, :decimal do
      allow_nil? true
      public? true
      description "最近一次投注金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :last_bet_time, :utc_datetime do
      allow_nil? true
      public? true
      description "最近一次投注时间"
    end

    # 重置记录
    attribute :reset_time, :utc_datetime do
      allow_nil? true
      public? true
      description "最近一次重置时间"
    end

    attribute :reset_reason, :string do
      allow_nil? true
      public? true
      description "重置原因"
      constraints max_length: 200
    end

    attribute :reset_count, :integer do
      allow_nil? false
      public? true
      description "重置次数"
      default 0
      constraints min: 0
    end

    # 统计字段
    attribute :total_bonus_received, :decimal do
      allow_nil? false
      public? true
      description "累计收到奖励金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :total_bets_made, :decimal do
      allow_nil? false
      public? true
      description "累计投注金额（分）"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    # 客户端兼容性字段
    attribute :bonusmoney, :integer do
      allow_nil? false
      public? true
      description "奖金余额"
      default 0
    end

    attribute :bonuscashmoney, :integer do
      allow_nil? false
      public? true
      description "奖金现金余额"
      default 0
    end

    attribute :winningmoney, :integer do
      allow_nil? false
      public? true
      description "获胜奖金余额"
      default 0
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  # 计算字段
  calculations do
    calculate :remaining_turnover, :decimal, expr(required_turnover - completed_turnover) do
      description "剩余需要完成的流水金额"
    end

    calculate :completion_percentage,
              :decimal,
              expr(
                fragment(
                  "CASE WHEN ? > 0 THEN (? / ?) * 100 ELSE 0 END",
                  required_turnover,
                  completed_turnover,
                  required_turnover
                )
              ) do
      description "流水完成百分比"
    end

    calculate :is_completed, :boolean, expr(completed_turnover >= required_turnover) do
      description "是否已完成流水要求"
    end
  end

  identities do
    identity :unique_user, [:user_id]
  end

  # 辅助函数
  defp maybe_change_attribute(changeset, attribute, value) do
    case value do
      nil -> changeset
      value -> Ash.Changeset.change_attribute(changeset, attribute, value)
    end
  end
end
