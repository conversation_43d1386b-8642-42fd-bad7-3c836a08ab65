defmodule Teen.Workers.LuckUpdateWorker do
  @moduledoc """
  幸运值系统更新工作者

  通过Oban异步处理幸运值相关更新，确保幸运值系统故障不影响充值核心流程
  """

  use Oban.Worker, queue: :luck_updates, max_attempts: 3

  require Logger
  alias Teen.Services.LuckService

  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{"event_type" => "recharge_completed", "user_id" => user_id, "amount" => amount}
      }) do
    Logger.info("🍀 [LUCK_WORKER] 处理充值完成事件 - 用户=#{user_id}, 金额=#{amount}")

    try do
      # 增加充值幸运值（750，上限1000）
      case LuckService.add_recharge_luck(user_id) do
        {:ok, luck_record} ->
          Logger.info("🍀 [LUCK_WORKER] 用户 #{user_id} 充值成功，幸运值更新为: #{luck_record.current_luck}")
          :ok

        {:error, reason} ->
          Logger.error("🍀 [LUCK_WORKER] 用户 #{user_id} 幸运值更新失败: #{inspect(reason)}")
          # 返回错误，让Oban进行重试
          {:error, "幸运值更新失败: #{inspect(reason)}"}
      end
    rescue
      error ->
        Logger.error("🍀 [LUCK_WORKER] 执行异常: #{inspect(error)}")
        Logger.error("Stack trace: #{Exception.format_stacktrace()}")

        # 返回错误，让Oban进行重试
        {:error, "幸运值处理失败: #{Exception.message(error)}"}
    end
  end

  def perform(%Oban.Job{args: args}) do
    Logger.warning("🍀 [LUCK_WORKER] 未识别的任务类型: #{inspect(args)}")
    :ok
  end
end
