<Backpex.HTML.Layout.app_shell fluid={@fluid?}>
  <:topbar>
    <!-- 品牌标识区域 -->
    <div class="flex items-center gap-3">
      <Backpex.HTML.Layout.topbar_branding />
      <!-- 系统标题 -->
      <div class="hidden lg:flex flex-col">
        <span class="text-sm font-semibold text-base-content">Teen Patti 管理系统</span>
        <span class="text-xs text-base-content/60">Admin Dashboard</span>
      </div>
    </div>

    <!-- 中间工具栏区域 -->
    <div class="flex-1 flex items-center justify-center gap-4">
      <!-- 快速搜索框 -->
      <div class="hidden xl:flex items-center max-w-md w-full">
        <div class="relative w-full">
          <input
            type="text"
            placeholder="快速搜索用户、订单..."
            class="input input-sm input-bordered w-full pl-10 pr-12 bg-base-100/50 backdrop-blur-sm border-base-300/50 focus:border-primary/50 focus:bg-base-100 transition-all duration-200"
          />
          <.icon name="hero-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-base-content/60" />
          <kbd class="kbd kbd-xs absolute right-2 top-1/2 transform -translate-y-1/2 text-xs">⌘K</kbd>
        </div>
      </div>

      <!-- 系统状态指示器 -->
      <div class="hidden lg:flex items-center gap-3">
        <div class="tooltip tooltip-bottom" data-tip="在线用户">
          <div class="flex items-center gap-1 px-2 py-1 bg-success/10 rounded-full">
            <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
            <span class="text-xs font-medium text-success">1,234</span>
          </div>
        </div>
        <div class="tooltip tooltip-bottom" data-tip="今日收入">
          <div class="flex items-center gap-1 px-2 py-1 bg-warning/10 rounded-full">
            <.icon name="hero-currency-dollar" class="size-3 text-warning" />
            <span class="text-xs font-medium text-warning">¥12.3K</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧工具区域 -->
    <div class="flex items-center gap-2">
      <!-- 通知按钮 -->
      <div class="dropdown dropdown-end">
        <label tabindex="0" class="btn btn-ghost btn-sm btn-circle relative hover:bg-base-200/50 transition-colors">
          <.icon name="hero-bell" class="size-5" />
          <!-- 通知徽章 -->
          <div class="absolute -top-1 -right-1 bg-error text-error-content rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold animate-pulse">
            3
          </div>
        </label>
        <div tabindex="0" class="dropdown-content z-[1] menu p-0 shadow-xl bg-base-100 rounded-box w-80 border border-base-300/50 backdrop-blur-sm">
          <div class="p-4 border-b border-base-300/50 bg-gradient-to-r from-primary/5 to-secondary/5">
            <div class="flex items-center justify-between">
              <h3 class="font-semibold text-base-content">通知中心</h3>
              <div class="badge badge-primary badge-sm">3 新</div>
            </div>
          </div>
          <div class="max-h-64 overflow-y-auto">
            <div class="p-3 hover:bg-base-200/50 cursor-pointer border-b border-base-300/30 transition-colors">
              <div class="flex items-start gap-3">
                <div class="avatar placeholder">
                  <div class="bg-primary text-primary-content rounded-full w-8 h-8">
                    <.icon name="hero-user-plus" class="size-4" />
                  </div>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium">新用户注册</p>
                  <p class="text-xs text-base-content/60">用户 user123 刚刚注册</p>
                  <p class="text-xs text-base-content/40 mt-1">2分钟前</p>
                </div>
                <div class="w-2 h-2 bg-primary rounded-full flex-shrink-0 mt-2"></div>
              </div>
            </div>
            <div class="p-3 hover:bg-base-200/50 cursor-pointer border-b border-base-300/30 transition-colors">
              <div class="flex items-start gap-3">
                <div class="avatar placeholder">
                  <div class="bg-warning text-warning-content rounded-full w-8 h-8">
                    <.icon name="hero-exclamation-triangle" class="size-4" />
                  </div>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium">异常充值订单</p>
                  <p class="text-xs text-base-content/60">订单 #12345 需要人工审核</p>
                  <p class="text-xs text-base-content/40 mt-1">5分钟前</p>
                </div>
                <div class="w-2 h-2 bg-warning rounded-full flex-shrink-0 mt-2"></div>
              </div>
            </div>
            <div class="p-3 hover:bg-base-200/50 cursor-pointer transition-colors">
              <div class="flex items-start gap-3">
                <div class="avatar placeholder">
                  <div class="bg-error text-error-content rounded-full w-8 h-8">
                    <.icon name="hero-cpu-chip" class="size-4" />
                  </div>
                </div>
                <div class="flex-1">
                  <p class="text-sm font-medium">系统警告</p>
                  <p class="text-xs text-base-content/60">服务器CPU使用率过高</p>
                  <p class="text-xs text-base-content/40 mt-1">10分钟前</p>
                </div>
                <div class="w-2 h-2 bg-error rounded-full flex-shrink-0 mt-2"></div>
              </div>
            </div>
          </div>
          <div class="p-3 border-t border-base-300/50 bg-base-50">
            <div class="flex gap-2">
              <button class="btn btn-sm btn-ghost flex-1">全部已读</button>
              <button class="btn btn-sm btn-primary flex-1">查看全部</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 主题选择器 -->
      <Backpex.HTML.Layout.theme_selector
        socket={@socket}
        themes={[
          {"Light", "light"},
          {"Dark", "dark"},
          {"Cyberpunk", "cyberpunk"}
        ]}
      />

      <!-- 用户信息区域 -->
      <div class="flex items-center gap-3 ml-2">
        <!-- 桌面端用户信息文本 -->
        <div class="hidden lg:flex flex-col items-end text-sm">
          <span class="font-medium text-base-content">
            <%= if assigns[:current_user] && @current_user.username do %>
              {@current_user.username}
            <% else %>
              未知用户
            <% end %>
          </span>
          <span class="text-xs text-base-content/60">
            <%= if assigns[:current_user] && @current_user.role_name do %>
              {@current_user.role_name}
            <% else %>
              普通用户
            <% end %>
          </span>
        </div>

        <!-- 用户头像和下拉菜单 -->
        <Backpex.HTML.Layout.topbar_dropdown class="dropdown-end">
          <:label>
            <label tabindex="0" class="btn btn-ghost flex items-center gap-2 px-3 hover:bg-base-200/50 transition-colors">
              <!-- 用户头像 -->
              <div class="avatar placeholder">
                <div class="bg-gradient-to-br from-primary to-secondary text-primary-content rounded-full w-9 h-9 ring-2 ring-primary/20">
                  <span class="text-sm font-bold">
                    <%= if assigns[:current_user] && @current_user.username do %>
                      {String.first(to_string(@current_user.username))}
                    <% else %>
                      U
                    <% end %>
                  </span>
                </div>
              </div>
              <!-- 移动端显示用户名 -->
              <div class="lg:hidden flex flex-col items-start">
                <span class="text-sm font-medium">
                  <%= if assigns[:current_user] && @current_user.username do %>
                    {@current_user.username}
                  <% else %>
                    未知用户
                  <% end %>
                </span>
                <span class="text-xs opacity-60">
                  <%= if assigns[:current_user] && @current_user.role_name do %>
                    {@current_user.role_name}
                  <% else %>
                    普通用户
                  <% end %>
                </span>
              </div>
              <.icon name="hero-chevron-down" class="size-4 transition-transform duration-200" />
            </label>
          </:label>
          <!-- 用户菜单 -->
          <li>
            <.link navigate="/admin/profile" class="flex items-center gap-3 px-4 py-3 hover:bg-base-200 transition-colors">
              <.icon name="hero-user-circle" class="size-5 text-primary" />
              <div class="flex flex-col">
                <span class="font-medium">个人信息</span>
                <span class="text-xs text-base-content/60">查看和编辑个人资料</span>
              </div>
            </.link>
          </li>
          <li>
            <.link navigate="/admin/system-config" class="flex items-center gap-3 px-4 py-3 hover:bg-base-200 transition-colors">
              <.icon name="hero-cog-6-tooth" class="size-5 text-info" />
              <div class="flex flex-col">
                <span class="font-medium">系统设置</span>
                <span class="text-xs text-base-content/60">配置系统参数</span>
              </div>
            </.link>
          </li>
          <!-- 分隔线 -->
          <li><hr class="my-2 border-base-300" /></li>
          <!-- 退出登录 -->
          <li>
            <.link
              navigate={~p"/sign-out"}
              class="flex items-center gap-3 px-4 py-3 text-error hover:bg-error/10 transition-colors"
            >
              <.icon name="hero-arrow-right-on-rectangle" class="size-5" />
              <div class="flex flex-col">
                <span class="font-medium">退出登录</span>
                <span class="text-xs text-error/60">安全退出系统</span>
              </div>
            </.link>
          </li>
        </Backpex.HTML.Layout.topbar_dropdown>
      </div>
    </div>
  </:topbar>
  <:sidebar>
    <!-- 用户管理 -->
    <.sidebar_section id="user-management">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-users" class="size-4 text-primary" />
          <span class="font-semibold">用户管理</span>
        </div>
      </:label>
      <.sidebar_item current_url={@current_url} navigate={~p"/admin/users"} icon="hero-user" text="用户列表" color="primary" />
      <.sidebar_item current_url={@current_url} navigate="/admin/user-management" icon="hero-users" text="用户管理" color="primary" />
      <.sidebar_item current_url={@current_url} navigate="/admin/subordinate-management" icon="hero-user-group" text="下线管理" color="primary" />
      <.sidebar_item current_url={@current_url} navigate={~p"/admin/user-bans"} icon="hero-no-symbol" text="封禁管理" color="primary" />
      <.sidebar_item current_url={@current_url} navigate={~p"/admin/user-devices"} icon="hero-device-phone-mobile" text="设备管理" color="primary" />
    </.sidebar_section>

    <!-- 活动系统 -->
    <.sidebar_section id="activity-system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-gift" class="size-4 text-secondary" />
          <span class="font-semibold">活动系统</span>
        </div>
      </:label>
      <!-- 日常活动 -->
      <.sidebar_subgroup title="日常活动">
        <.sidebar_subitem current_url={@current_url} navigate="/admin/daily-game-tasks" icon="hero-calendar-days" text="每日任务" color="secondary" />
        <.sidebar_subitem current_url={@current_url} navigate="/admin/seven-day-logins" icon="hero-calendar" text="七日登录" color="secondary" />
        <.sidebar_subitem current_url={@current_url} navigate="/admin/weekly-cards" icon="hero-credit-card" text="周卡活动" color="secondary" />
      </.sidebar_subgroup>

      <!-- 充值活动 -->
      <.sidebar_subgroup title="充值活动">
        <.sidebar_subitem current_url={@current_url} navigate="/admin/recharge-tasks" icon="hero-banknotes" text="充值任务" color="warning" />
        <.sidebar_subitem current_url={@current_url} navigate="/admin/first-recharge-gifts" icon="hero-gift" text="首充礼包" color="warning" />
        <.sidebar_subitem current_url={@current_url} navigate="/admin/vip-gifts" icon="hero-star" text="VIP礼包" color="warning" />
      </.sidebar_subgroup>

      <!-- 游戏活动 -->
      <.sidebar_subgroup title="游戏活动">
        <.sidebar_subitem current_url={@current_url} navigate="/admin/recharge-wheels" icon="hero-arrow-path" text="转盘抽奖" color="accent" />
        <.sidebar_subitem current_url={@current_url} navigate="/admin/scratch-card-activities" icon="hero-ticket" text="刮刮卡" color="accent" />
        <.sidebar_subitem current_url={@current_url} navigate="/admin/loss-rebates" icon="hero-arrow-uturn-left" text="亏损返利" color="accent" />
      </.sidebar_subgroup>

      <!-- 推广活动 -->
      <.sidebar_subgroup title="推广活动">
        <.sidebar_subitem current_url={@current_url} navigate="/admin/invite-cash-activities" icon="hero-user-plus" text="邀请奖励" color="info" />
        <.sidebar_subitem current_url={@current_url} navigate="/admin/binding-rewards" icon="hero-link" text="绑定奖励" color="info" />
        <.sidebar_subitem current_url={@current_url} navigate="/admin/free-bonus-tasks" icon="hero-hand-raised" text="免费任务" color="info" />
        <.sidebar_subitem current_url={@current_url} navigate="/admin/cdkey-rewards" icon="hero-key" text="CDKey奖励" color="info" />
      </.sidebar_subgroup>
    </.sidebar_section>

    <!-- 活动记录 -->
    <.sidebar_section id="activity-records">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-clipboard-document-list" class="size-4 text-neutral" />
          <span class="font-semibold">活动记录</span>
        </div>
      </:label>
      <.sidebar_item current_url={@current_url} navigate="/admin/user-activity-participations" icon="hero-user-circle" text="参与记录" color="neutral" />
      <.sidebar_item current_url={@current_url} navigate="/admin/reward-claim-records" icon="hero-clipboard-document-check" text="奖励记录" color="neutral" />
    </.sidebar_section>

    <!-- 游戏管理 -->
    <.sidebar_section id="game-management">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-puzzle-piece" class="size-4 text-error" />
          <span class="font-semibold">游戏管理</span>
        </div>
      </:label>
      <.sidebar_item current_url={@current_url} navigate={~p"/admin/games"} icon="hero-squares-2x2" text="游戏列表" color="error" />
      <.sidebar_item current_url={@current_url} navigate="/admin/robots" icon="hero-building-office-2" text="游戏房间" color="error" />
      <.sidebar_item current_url={@current_url} navigate="/admin/jackpots" icon="hero-trophy" text="奖池管理" color="warning" />
      <.sidebar_item current_url={@current_url} navigate="/admin/inventory_control/" icon="hero-chart-bar" text="游戏统计" color="info" />
    </.sidebar_section>

    <!-- 支付系统 -->
    <.sidebar_section id="payment-system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-credit-card" class="size-4 text-success" />
          <span class="font-semibold">支付系统</span>
        </div>
      </:label>
      <!-- 支付配置 -->
      <.sidebar_subgroup title="支付配置">
        <.sidebar_subitem current_url={@current_url} navigate={~p"/admin/payment-configs"} icon="hero-credit-card" text="支付配置" color="success" />
        <.sidebar_subitem current_url={@current_url} navigate={~p"/admin/payment-gateways"} icon="hero-server" text="支付网关" color="success" />
        <.sidebar_subitem current_url={@current_url} navigate={~p"/admin/bank-configs"} icon="hero-building-library" text="银行配置" color="success" />
      </.sidebar_subgroup>

      <!-- 提现管理 -->
      <.sidebar_subgroup title="提现管理">
        <.sidebar_subitem current_url={@current_url} navigate={~p"/admin/withdrawal-configs"} icon="hero-arrow-path-rounded-square" text="提现配置" color="warning" />
        <.sidebar_subitem current_url={@current_url} navigate={~p"/admin/withdrawal-records"} icon="hero-document-text" text="提现记录" color="warning" />
        <.sidebar_subitem current_url={@current_url} navigate={~p"/admin/user-bank-cards"} icon="hero-identification" text="用户银行卡" color="warning" />
      </.sidebar_subgroup>

      <!-- 订单管理 -->
      <.sidebar_subgroup title="订单管理">
        <.sidebar_subitem current_url={@current_url} navigate={~p"/admin/payment-orders"} icon="hero-document-text" text="支付订单" color="info" />
      </.sidebar_subgroup>
    </.sidebar_section>

    <!-- 商品系统 -->
    <.sidebar_section id="shop-system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-shopping-bag" class="size-4 text-accent" />
          <span class="font-semibold">商品系统</span>
        </div>
      </:label>
      <.sidebar_item current_url={@current_url} navigate={~p"/admin/products"} icon="hero-cube" text="商品管理" color="accent" />
      <.sidebar_item current_url={@current_url} navigate={~p"/admin/product-templates"} icon="hero-document-duplicate" text="商品模板" color="accent" />
      <.sidebar_item current_url={@current_url} navigate={~p"/admin/user-purchases"} icon="hero-receipt-percent" text="购买记录" color="accent" />
    </.sidebar_section>

    <!-- 系统管理 -->
    <.sidebar_section id="system">
      <:label>
        <div class="flex items-center gap-2">
          <.icon name="hero-cog-6-tooth" class="size-4 text-neutral" />
          <span class="font-semibold">系统管理</span>
        </div>
      </:label>
      <.sidebar_item current_url={@current_url} navigate="/admin/system-config" icon="hero-adjustments-horizontal" text="系统配置" color="neutral" />
      <.sidebar_item current_url={@current_url} navigate="/admin/channels" icon="hero-tv" text="渠道管理" color="neutral" />
      <.sidebar_item current_url={@current_url} navigate="/admin/logs" icon="hero-document-text" text="系统日志" color="neutral" />
    </.sidebar_section>

    <!-- 侧边栏底部信息 -->
    <div class="mt-auto p-4 border-t border-base-300 bg-gradient-to-t from-base-200/30 to-transparent">
      <div class="space-y-3">
        <!-- 系统信息 -->
        <div class="text-center">
          <div class="text-xs font-semibold text-base-content/80 mb-1">Teen Patti Admin</div>
          <div class="text-xs text-base-content/50">v1.0.0</div>
        </div>

        <!-- 系统状态 -->
        <div class="flex items-center justify-center gap-2 p-2 bg-base-100/50 rounded-lg">
          <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
          <span class="text-xs text-base-content/70 font-medium">系统运行正常</span>
        </div>

        <!-- 快速统计 -->
        <div class="grid grid-cols-2 gap-2 text-center">
          <div class="p-2 bg-primary/10 rounded-lg">
            <div class="text-xs font-bold text-primary">1,234</div>
            <div class="text-xs text-base-content/60">在线用户</div>
          </div>
          <div class="p-2 bg-success/10 rounded-lg">
            <div class="text-xs font-bold text-success">99.9%</div>
            <div class="text-xs text-base-content/60">系统稳定</div>
          </div>
        </div>
      </div>
    </div>
  </:sidebar>

  <!-- Flash消息 -->
  <Backpex.HTML.Layout.flash_messages flash={@flash} />

  <!-- 主内容区域 -->
  <div class="main-content-area">
    <!-- 背景装饰层 -->
    <div class="background-decorations">
      <div class="bg-gradient-primary"></div>
      <div class="bg-gradient-secondary"></div>
      <div class="bg-gradient-accent"></div>
    </div>

    <!-- 内容容器 -->
    <div class="content-container">
      <div class="content-wrapper">
        {@inner_content}
      </div>
    </div>
  </div>
</Backpex.HTML.Layout.app_shell>

<!-- 扩大主内容空间的样式 -->
<style>
  /* ===== 主内容区域最大化样式 ===== */
  .main-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    /* 移除默认的内边距和边距 */
    margin: 0;
    padding: 0;
    /* 确保占满所有可用空间 */
    width: 100%;
    height: 100vh;
  }

  /* 背景装饰层 */
  .background-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
  }

  .bg-gradient-primary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
      rgba(102, 126, 234, 0.03) 0%,
      rgba(118, 75, 162, 0.03) 100%);
  }

  .bg-gradient-secondary {
    position: absolute;
    top: 0;
    right: 0;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle at 80% 20%,
      rgba(255, 119, 198, 0.05) 0%,
      transparent 50%);
  }

  .bg-gradient-accent {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50%;
    height: 50%;
    background: radial-gradient(circle at 20% 80%,
      rgba(120, 219, 255, 0.05) 0%,
      transparent 50%);
  }

  /* 内容容器 */
  .content-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 10;
    /* 最小化内边距 */
    padding: 8px;
    /* 确保占满可用空间 */
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  /* 内容包装器 */
  .content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    /* 移除边距和内边距 */
    margin: 0;
    padding: 0;
    /* 确保占满父容器 */
    width: 100%;
    height: 100%;
    /* 允许内容滚动 */
    overflow-y: auto;
    overflow-x: hidden;
    /* 优化滚动性能 */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
  }

  /* 自定义滚动条样式 */
  .content-wrapper::-webkit-scrollbar {
    width: 6px;
  }

  .content-wrapper::-webkit-scrollbar-track {
    background: transparent;
  }

  .content-wrapper::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  .content-wrapper::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
  }

  /* 确保所有子元素都能占满空间 */
  .content-wrapper > * {
    flex: 1;
    width: 100%;
  }

  /* Backpex 组件样式优化 */
  .content-wrapper .backpex-content,
  .content-wrapper [data-backpex],
  .content-wrapper .live-view {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* 表格和列表组件优化 */
  .content-wrapper table,
  .content-wrapper .table-container,
  .content-wrapper .data-table {
    width: 100%;
    margin: 0;
  }

  /* 表单组件优化 */
  .content-wrapper form,
  .content-wrapper .form-container {
    width: 100%;
    margin: 0;
  }

  /* 卡片和面板优化 */
  .content-wrapper .card,
  .content-wrapper .panel,
  .content-wrapper .widget {
    margin: 4px 0;
    width: 100%;
  }

  /* 响应式优化 */
  @media (max-width: 768px) {
    .content-container {
      padding: 4px;
    }

    .content-wrapper {
      /* 移动端进一步减少间距 */
      gap: 0;
    }

    .content-wrapper .card,
    .content-wrapper .panel {
      margin: 2px 0;
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .content-container {
      padding: 6px;
    }
  }

  @media (min-width: 1025px) {
    .content-container {
      padding: 8px;
    }
  }

  /* 大屏幕优化 */
  @media (min-width: 1440px) {
    .content-container {
      padding: 12px;
    }
  }

  /* 超大屏幕优化 */
  @media (min-width: 1920px) {
    .content-container {
      padding: 16px;
    }
  }

  /* 确保 Backpex 布局组件占满空间 */
  .main-content-area .backpex-layout,
  .main-content-area .backpex-app-shell,
  .main-content-area .backpex-main {
    flex: 1;
    height: 100%;
    width: 100%;
  }

  /* 优化页面加载性能 */
  .main-content-area {
    contain: layout style paint;
    will-change: transform;
  }

  /* 确保内容不会被侧边栏遮挡 */
  @media (min-width: 1024px) {
    .main-content-area {
      /* 桌面端确保不被侧边栏影响 */
      width: 100%;
    }
  }

  /* 打印样式优化 */
  @media print {
    .main-content-area {
      height: auto;
      min-height: auto;
      overflow: visible;
    }

    .background-decorations {
      display: none;
    }

    .content-container {
      padding: 0;
    }
  }

  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .bg-gradient-primary,
    .bg-gradient-secondary,
    .bg-gradient-accent {
      opacity: 0.1;
    }
  }

  /* 减少动画模式支持 */
  @media (prefers-reduced-motion: reduce) {
    .content-wrapper {
      scroll-behavior: auto;
    }
  }

  /* 深色模式优化 */
  @media (prefers-color-scheme: dark) {
    .bg-gradient-primary {
      background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.05) 0%,
        rgba(118, 75, 162, 0.05) 100%);
    }

    .content-wrapper::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
    }

    .content-wrapper::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
</style>
