defmodule Teen.Components.IconTest do
  @moduledoc """
  Icon validation and testing utilities for the Teen Patti admin system.
  """

  @doc """
  List of all icons used in the admin layout
  """
  def admin_layout_icons do
    [
      # User Management
      "hero-users",
      "hero-user",
      "hero-user-group", 
      "hero-no-symbol",
      "hero-device-phone-mobile",
      
      # Activity System
      "hero-gift",
      "hero-calendar-days",
      "hero-calendar",
      "hero-credit-card",
      "hero-banknotes",
      "hero-star",
      "hero-arrow-path",
      "hero-ticket",
      "hero-arrow-uturn-left",
      "hero-user-plus",
      "hero-link",
      "hero-hand-raised",
      "hero-key",
      
      # Activity Records
      "hero-clipboard-document-list",
      "hero-user-circle",
      "hero-clipboard-document-check",
      
      # Game Management
      "hero-puzzle-piece",
      "hero-squares-2x2",
      "hero-building-office",
      "hero-trophy",
      "hero-chart-bar",
      
      # Payment System
      "hero-server",
      "hero-building-library",
      "hero-arrow-path-rounded-square",
      "hero-document-text",
      "hero-identification",
      
      # Shop System
      "hero-shopping-bag",
      "hero-cube",
      "hero-document-duplicate",
      "hero-receipt-percent",
      
      # System Management
      "hero-cog-6-tooth",
      "hero-adjustments-horizontal",
      "hero-tv"
    ]
  end

  @doc """
  Validates that all icons exist in the heroicons library
  """
  def validate_icons do
    icons = admin_layout_icons()
    
    Enum.map(icons, fn icon ->
      icon_name = String.replace_prefix(icon, "hero-", "")
      icon_path = "deps/heroicons/optimized/24/outline/#{icon_name}.svg"
      
      case File.exists?(icon_path) do
        true -> {:ok, icon}
        false -> {:error, icon, "Icon file not found: #{icon_path}"}
      end
    end)
  end

  @doc """
  Returns a list of missing icons
  """
  def missing_icons do
    validate_icons()
    |> Enum.filter(fn
      {:error, _, _} -> true
      _ -> false
    end)
  end

  @doc """
  Returns true if all icons are valid
  """
  def all_icons_valid? do
    missing_icons() == []
  end
end
