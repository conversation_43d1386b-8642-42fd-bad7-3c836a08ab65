# Icon Reference for Teen Patti Admin System

This document provides a comprehensive reference for all icons used in the admin system, along with alternatives and troubleshooting information.

## Icon System

The admin system uses [Heroicons](https://heroicons.com) for all interface icons. Icons are referenced with the `hero-` prefix followed by the icon name.

## Icons Used in Admin Layout

### User Management Section
- `hero-users` - User management section header
- `hero-user` - Individual user icon
- `hero-user-group` - User groups/subordinates
- `hero-no-symbol` - Ban/restriction symbol
- `hero-device-phone-mobile` - Device management

### Activity System Section
- `hero-gift` - Activity system header and gifts
- `hero-calendar-days` - Daily tasks
- `hero-calendar` - Seven-day login
- `hero-credit-card` - Weekly cards and payment
- `hero-banknotes` - Recharge tasks
- `hero-star` - VIP gifts
- `hero-arrow-path` - Wheel/rotation activities
- `hero-ticket` - Scratch cards
- `hero-arrow-uturn-left` - Loss rebates
- `hero-user-plus` - Invite rewards
- `hero-link` - Binding rewards
- `hero-hand-raised` - Free tasks
- `hero-key` - <PERSON><PERSON>ey rewards

### Activity Records Section
- `hero-clipboard-document-list` - Activity records header
- `hero-user-circle` - User participation
- `hero-clipboard-document-check` - <PERSON><PERSON> claims

### Game Management Section
- `hero-puzzle-piece` - Game management header
- `hero-squares-2x2` - Game list
- `hero-building-office-2` - Game rooms (updated from building-office)
- `hero-trophy` - Jackpots
- `hero-chart-bar` - Game statistics

### Payment System Section
- `hero-credit-card` - Payment system header and configs
- `hero-server` - Payment gateways
- `hero-building-library` - Bank configurations
- `hero-arrow-path-rounded-square` - Withdrawal configs
- `hero-document-text` - Records and orders
- `hero-identification` - User bank cards

### Shop System Section
- `hero-shopping-bag` - Shop system header
- `hero-cube` - Product management
- `hero-document-duplicate` - Product templates
- `hero-receipt-percent` - Purchase records

### System Management Section
- `hero-cog-6-tooth` - System management header
- `hero-adjustments-horizontal` - System configuration
- `hero-tv` - Channel management

## Alternative Icons

If any icon is not rendering properly, here are suitable alternatives:

### User Management Alternatives
- `hero-user` → `hero-user-circle`
- `hero-users` → `hero-user-group`
- `hero-device-phone-mobile` → `hero-computer-desktop`

### Activity System Alternatives
- `hero-gift` → `hero-present`
- `hero-calendar-days` → `hero-calendar`
- `hero-arrow-path` → `hero-arrow-path-rounded-square`
- `hero-ticket` → `hero-document`
- `hero-hand-raised` → `hero-hand-thumb-up`

### Game Management Alternatives
- `hero-squares-2x2` → `hero-square-3-stack-3d`
- `hero-building-office-2` → `hero-building-office`
- `hero-puzzle-piece` → `hero-cube`

### Payment System Alternatives
- `hero-building-library` → `hero-building-office`
- `hero-arrow-path-rounded-square` → `hero-arrow-path`
- `hero-receipt-percent` → `hero-document-text`

## Troubleshooting

### Icon Not Displaying
1. Verify the icon name is correct and exists in heroicons
2. Check that the `hero-` prefix is included
3. Ensure the icon file exists in `deps/heroicons/optimized/24/outline/`
4. Try using an alternative icon from the list above

### CSS Issues
1. Verify the icon component is properly imported
2. Check that Tailwind CSS is processing the heroicons plugin
3. Ensure the icon classes are being applied correctly

### Testing Icons
Use the `Teen.Components.IconTest` module to validate all icons:

```elixir
# Check if all icons are valid
Teen.Components.IconTest.all_icons_valid?()

# Get list of missing icons
Teen.Components.IconTest.missing_icons()

# Validate all icons
Teen.Components.IconTest.validate_icons()
```

## Icon Component Usage

```heex
<.icon name="hero-user" class="size-5 text-primary" />
```

The icon component accepts:
- `name` - The heroicon name with `hero-` prefix
- `class` - CSS classes for styling (size, color, etc.)
