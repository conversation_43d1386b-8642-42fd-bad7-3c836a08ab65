defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Crash.CrashLogic do
  @moduledoc """
  Crash游戏逻辑模块

  处理游戏的核心逻辑，包括：
  - 爆炸点计算
  - 倍数计算
  - 收益计算
  - 游戏状态管理
  """

  alias Cypridina.Teen.GameSystem.Games.Crash.{CrashGame}
  require Logger

  @doc """
  计算爆炸倍数和时间

  基于C++代码的CalculateStayTime算法，考虑以下因素：
  - 是否有真实玩家下注
  - 库存控制状态
  - 玩家控制值
  - 充值提现比例

  返回: {multiplier, crash_time_ms}
  """
  def calculate_crash_point(state, betting_players \\ []) do
    config = state.game_data.config

    # 计算爆炸倍数
    multiplier =
      if Enum.empty?(betting_players) do
        # 没有玩家下注时的随机算法
        calculate_no_bet_multiplier()
      else
        # 有玩家下注时的复杂算法
        calculate_with_bet_multiplier(state, betting_players)
      end

    # 根据倍数查找对应的时间
    crash_time = get_crash_time_by_multiplier(multiplier, config)

    Logger.info("🎲 [CRASH_LOGIC] 计算爆炸点 - 倍数: #{multiplier / 100}x, 时间: #{crash_time}ms")

    {multiplier, crash_time}
  end

  @doc """
  根据时间获取当前倍数
  """
  def get_current_multiplier(elapsed_time_ms, _config) do
    # 基于C++的倍数计算公式：倍数 = 100 + elapsed_time_ms / 20
    # 每20毫秒增加1个点（0.01倍）
    multiplier = 100 + div(elapsed_time_ms, 20)
    # 最小倍数为1.00x
    max(multiplier, 100)
  end

  @doc """
  根据倍数查找对应的爆炸时间
  """
  def get_crash_time_by_multiplier(multiplier, config) do
    # 查找配置表中对应的时间
    multiplier_configs = Map.get(config, :multiplier_configs, [])

    # 找到第一个大于等于目标倍数的配置项
    found_config =
      Enum.find(multiplier_configs, fn config_item ->
        config_item.multiplier >= multiplier
      end)

    case found_config do
      nil ->
        # 如果没找到，使用最大飞行时间
        Map.get(config, :max_fly_time, 110_460)

      config_item ->
        config_item.time
    end
  end

  @doc """
  计算玩家下车收益
  """
  def calculate_cash_out_payout(bet_amount, cash_out_multiplier, config) do
    revenue_ratio = Map.get(config, :revenue_ratio, 50)
    CrashGame.calculate_payout(bet_amount, cash_out_multiplier, revenue_ratio)
  end

  @doc """
  验证玩家是否可以下注
  """
  def can_place_bet?(state, player, bet_amount) do
    cond do
      state.game_data.phase != :betting ->
        {:error, CrashGame.error_codes().not_betting_phase}

      Map.get(player, :bet_amount, 0) > 0 ->
        {:error, CrashGame.error_codes().already_bet}

      not CrashGame.valid_bet_amount?(bet_amount, state.game_data.config) ->
        {:error, CrashGame.error_codes().insufficient_balance}

      # TODO: 检查玩家余额
      # player.balance < bet_amount ->
      #   {:error, CrashGame.error_codes().insufficient_balance}

      true ->
        :ok
    end
  end

  @doc """
  验证玩家是否可以下车
  """
  def can_cash_out?(state, player) do
    cond do
      state.game_data.phase != :flying ->
        {:error, CrashGame.error_codes().not_flying_phase}

      Map.get(player, :bet_amount, 0) <= 0 ->
        {:error, CrashGame.error_codes().no_bet_placed}

      Map.get(player, :cashed_out, false) ->
        {:error, CrashGame.error_codes().already_cashed_out}

      true ->
        :ok
    end
  end

  @doc """
  处理玩家下注
  """
  def place_bet(state, player, bet_amount) do
    case can_place_bet?(state, player, bet_amount) do
      :ok ->
        updated_player =
          Map.merge(player, %{
            bet_amount: bet_amount,
            bet_time: System.system_time(:millisecond),
            cashed_out: false,
            cash_out_multiplier: nil
          })

        # 更新游戏数据
        updated_game_data =
          state.game_data
          |> Map.update(:total_bets, bet_amount, &(&1 + bet_amount))
          |> Map.update(:betting_players, [player.user_id], fn players ->
            [player.user_id | players] |> Enum.uniq()
          end)

        updated_state = %{
          state
          | players: Map.put(state.players, player.user_id, updated_player),
            game_data: updated_game_data
        }

        {:ok, updated_state, updated_player}

      error ->
        error
    end
  end

  @doc """
  处理玩家下车
  """
  def cash_out(state, player, current_time_ms) do
    case can_cash_out?(state, player) do
      :ok ->
        config = state.game_data.config
        current_multiplier = get_current_multiplier(current_time_ms, config)
        payout_info = calculate_cash_out_payout(player.bet_amount, current_multiplier, config)

        updated_player =
          Map.merge(player, %{
            cashed_out: true,
            cash_out_time: current_time_ms,
            cash_out_multiplier: current_multiplier,
            payout: payout_info.gross_payout,
            profit: payout_info.profit,
            revenue: payout_info.revenue
          })

        updated_state = %{state | players: Map.put(state.players, player.user_id, updated_player)}

        {:ok, updated_state, updated_player, payout_info}

      error ->
        error
    end
  end

  @doc """
  检查游戏是否应该爆炸
  """
  def should_crash?(state, current_time_ms) do
    crash_time = Map.get(state.game_data, :crash_time, 0)
    max_fly_time = Map.get(state.game_data.config, :max_fly_time, 110_460)

    current_time_ms >= crash_time or current_time_ms >= max_fly_time
  end

  @doc """
  处理游戏爆炸，结算未下车的玩家
  """
  def handle_crash(state, crash_time_ms) do
    config = state.game_data.config
    crash_multiplier = get_current_multiplier(crash_time_ms, config)

    # 处理所有未下车的玩家
    updated_players =
      state.players
      |> Enum.map(fn {user_id, player} ->
        if Map.get(player, :bet_amount, 0) > 0 and not Map.get(player, :cashed_out, false) do
          # 玩家没有下车，损失下注金额
          updated_player =
            Map.merge(player, %{
              crashed: true,
              crash_time: crash_time_ms,
              crash_multiplier: crash_multiplier,
              payout: 0,
              profit: -player.bet_amount
            })

          {user_id, updated_player}
        else
          {user_id, player}
        end
      end)
      |> Enum.into(%{})

    # 更新游戏数据
    updated_game_data =
      Map.merge(state.game_data, %{
        phase: :settling,
        crash_time: crash_time_ms,
        crash_multiplier: crash_multiplier,
        crashed: true
      })

    updated_state = %{state | players: updated_players, game_data: updated_game_data}

    {:ok, updated_state}
  end

  # ==================== 私有函数 ====================

  # 没有玩家下注时的倍数计算
  defp calculate_no_bet_multiplier() do
    rand = :rand.uniform(100)

    multiplier =
      cond do
        # 75%概率: 151-350
        rand <= 75 -> :rand.uniform(200) + 151
        # 18%概率: 350-550
        rand <= 93 -> :rand.uniform(200) + 350
        # 4%概率: 550-750
        rand <= 97 -> :rand.uniform(200) + 550
        # 3%概率: 750-950
        true -> :rand.uniform(200) + 750
      end

    Logger.info("🎲 [CRASH_LOGIC] 无玩家下注，生成倍数: #{multiplier}")
    multiplier
  end

  # 有玩家下注时的倍数计算（基于房间库存控制）
  defp calculate_with_bet_multiplier(state, betting_players) do
    Logger.info("🎲 [CRASH_LOGIC] 开始计算有玩家下注的倍数")

    # 计算总下注金额
    total_bet_amount =
      Enum.reduce(betting_players, 0, fn player, acc ->
        acc + Map.get(player, :bet_amount, 0)
      end)

    # 获取库存控制状态
    stock_control = get_stock_control_state(state)

    # 根据库存状态决定倍数策略
    case stock_control.status do
      :high ->
        # 高库存：放分，提高高倍数概率
        calculate_high_stock_multiplier(total_bet_amount)

      :low ->
        # 低库存：收分，降低高倍数概率
        calculate_low_stock_multiplier(total_bet_amount)

      :safe ->
        # 安全库存：正常随机
        calculate_safe_stock_multiplier(total_bet_amount)
    end
  end

  # 高库存状态：放分，提高高倍数概率
  defp calculate_high_stock_multiplier(total_bet_amount) do
    Logger.info("🎲 [CRASH_LOGIC] 高库存放分 - 总下注金额: #{total_bet_amount}")

    # 高库存时提高高倍数概率，让玩家更容易赢
    multiplier =
      case :rand.uniform(100) do
        # 40%: 2.00x-5.00x
        n when n <= 40 -> 200 + :rand.uniform(300)
        # 25%: 5.00x-10.00x
        n when n <= 65 -> 500 + :rand.uniform(500)
        # 15%: 10.00x-20.00x
        n when n <= 80 -> 1000 + :rand.uniform(1000)
        # 10%: 20.00x-30.00x
        n when n <= 90 -> 2000 + :rand.uniform(1000)
        # 6%: 30.00x-50.00x
        n when n <= 96 -> 3000 + :rand.uniform(2000)
        # 4%: 50.00x-100.00x
        _ -> 5000 + :rand.uniform(5000)
      end

    Logger.info("🎲 [CRASH_LOGIC] 高库存放分倍数: #{multiplier / 100}x")
    multiplier
  end

  # 低库存状态：收分，降低高倍数概率
  defp calculate_low_stock_multiplier(total_bet_amount) do
    Logger.info("🎲 [CRASH_LOGIC] 低库存收分 - 总下注金额: #{total_bet_amount}")

    # 低库存时降低高倍数概率，让系统更容易赢
    multiplier =
      case :rand.uniform(100) do
        # 70%: 0.96x-1.16x (玩家输)
        n when n <= 70 -> 96 + :rand.uniform(20)
        # 15%: 1.01x-1.51x
        n when n <= 85 -> 101 + :rand.uniform(50)
        # 10%: 1.51x-2.51x
        n when n <= 95 -> 151 + :rand.uniform(100)
        # 5%: 2.51x-4.01x
        _ -> 251 + :rand.uniform(150)
      end

    Logger.info("🎲 [CRASH_LOGIC] 低库存收分倍数: #{multiplier / 100}x")
    multiplier
  end

  # 安全库存状态：正常随机分布
  defp calculate_safe_stock_multiplier(total_bet_amount) do
    Logger.info("🎲 [CRASH_LOGIC] 安全库存随机 - 总下注金额: #{total_bet_amount}")

    # 安全库存时使用正常的随机分布
    multiplier =
      case :rand.uniform(100) do
        # 75%: 1.01x-3.01x
        n when n <= 75 -> 101 + :rand.uniform(200)
        # 15%: 3.01x-5.01x
        n when n <= 90 -> 301 + :rand.uniform(200)
        # 6%: 5.01x-8.01x
        n when n <= 96 -> 501 + :rand.uniform(300)
        # 3%: 8.01x-13.01x
        n when n <= 99 -> 801 + :rand.uniform(500)
        # 1%: 13.01x-23.01x
        _ -> 1301 + :rand.uniform(1000)
      end

    Logger.info("🎲 [CRASH_LOGIC] 安全库存随机倍数: #{multiplier / 100}x")
    multiplier
  end

  # 获取库存控制状态
  defp get_stock_control_state(state) do
    room_id = Map.get(state, :id, "default_room")

    # TODO: 后续从数据库获取真实库存数据
    # 暂时写死的库存配置
    current_stock = get_current_stock_amount(room_id)
    stock_center_line = get_stock_center_line(room_id)

    # 计算库存状态
    stock_status = determine_stock_status(current_stock, stock_center_line)

    Logger.info(
      "🏦 [STOCK_CONTROL] 房间:#{room_id}, 当前库存:#{current_stock}, 中心线:#{stock_center_line}, 状态:#{stock_status}"
    )

    %{
      current_stock: current_stock,
      center_line: stock_center_line,
      # :safe, :high, :low
      status: stock_status,
      room_id: room_id
    }
  end

  # 获取当前库存金额（暂时写死）
  defp get_current_stock_amount(room_id) do
    # TODO: 从数据库或Redis获取真实库存
    # 暂时返回模拟数据
    case room_id do
      # 安全状态
      "crash_room_1" -> 50000
      # 高库存状态
      "crash_room_2" -> 150_000
      # 低库存状态
      "crash_room_3" -> -20000
      # 默认安全状态
      _ -> 30000
    end
  end

  # 获取库存中心线（暂时写死）
  defp get_stock_center_line(room_id) do
    # TODO: 从配置表获取
    # 暂时返回固定值
    case room_id do
      "crash_room_1" -> 100_000
      "crash_room_2" -> 100_000
      "crash_room_3" -> 100_000
      # 默认中心线10万
      _ -> 100_000
    end
  end

  # 判断库存状态
  defp determine_stock_status(current_stock, center_line) do
    # 高库存阈值：中心线的1.5倍
    high_threshold = center_line * 1.5
    # 低库存阈值：中心线的0.3倍
    low_threshold = center_line * 0.3

    cond do
      # 高库存：需要放分
      current_stock >= high_threshold -> :high
      # 低库存：需要收分
      current_stock <= low_threshold -> :low
      # 安全区间：随机
      true -> :safe
    end
  end
end
