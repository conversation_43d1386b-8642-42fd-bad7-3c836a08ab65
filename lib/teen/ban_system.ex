defmodule Teen.BanSystem do
  @moduledoc """
  封号管理域

  包含用户封号、设备封锁、IP封锁、禁言等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  alias Teen.BanSystem.UserBan
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.User

  admin do
    show? true
  end

  resources do
    resource Teen.BanSystem.UserBan
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  封禁用户
  """
  def ban_user(user_id, ban_type, reason, operator_id, duration_hours \\ nil) do
    with {:ok, user} <- User.get_by_id(user_id),
         :ok <- validate_ban_operation(user, operator_id),
         {:ok, ban} <-
           UserBan.ban_user(%{
             user_id: user_id,
             ban_type: ban_type,
             reason: reason,
             operator_id: operator_id,
             duration_hours: duration_hours
           }) do
      # 记录用户当前资产
      update_ban_with_assets(ban, user)

      # 通知相关系统
      notify_user_banned(user, ban)

      {:ok, ban}
    end
  end

  @doc """
  解封用户
  """
  def unban_user(ban_id, operator_id, unban_reason \\ nil) do
    case UserBan.read(ban_id) do
      {:ok, ban} ->
        case UserBan.unban_user(ban, %{operator_id: operator_id, unban_reason: unban_reason}) do
          {:ok, updated_ban} ->
            notify_user_unbanned(ban.user_id, updated_ban)
            {:ok, updated_ban}

          {:error, reason} ->
            {:error, reason}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  检查用户是否被封禁
  """
  def is_user_banned?(user_id) do
    case UserBan.list_active_bans() do
      {:ok, bans} ->
        user_ban = Enum.find(bans, &(&1.user_id == user_id))

        case user_ban do
          nil -> {:ok, false}
          ban -> {:ok, true, ban}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量封禁用户
  """
  def batch_ban_users(user_ids, ban_type, reason, operator_id, duration_hours \\ nil)
      when is_list(user_ids) do
    results =
      Enum.map(user_ids, fn user_id ->
        ban_user(user_id, ban_type, reason, operator_id, duration_hours)
      end)

    {successes, failures} =
      Enum.split_with(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  @doc """
  延长封禁时间
  """
  def extend_ban(ban_id, additional_hours, operator_id) do
    case UserBan.read(ban_id) do
      {:ok, ban} ->
        UserBan.extend_ban(ban, %{additional_hours: additional_hours, operator_id: operator_id})

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  支付封禁用户
  """
  def ban_user_payment(user_id, reason, operator_id, duration_hours \\ nil) do
    ban_user(user_id, 4, reason, operator_id, duration_hours)
  end

  @doc """
  检查用户是否被支付封禁
  """
  def is_user_payment_banned?(user_id) do
    case UserBan.list_by_ban_type(4) do
      {:ok, payment_bans} ->
        active_ban =
          Enum.find(payment_bans, fn ban ->
            ban.user_id == user_id and
              ban.status == 1 and
              (is_nil(ban.expires_at) or
                 DateTime.compare(ban.expires_at, DateTime.utc_now()) == :gt)
          end)

        case active_ban do
          nil -> {:ok, false}
          ban -> {:ok, true, ban}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  解除用户支付封禁
  """
  def unban_user_payment(user_id, operator_id, unban_reason \\ "解除支付封禁") do
    case UserBan.list_by_user(user_id) do
      {:ok, bans} ->
        active_payment_ban =
          Enum.find(bans, fn ban ->
            ban.ban_type == 4 and
              ban.status == 1 and
              (is_nil(ban.expires_at) or
                 DateTime.compare(ban.expires_at, DateTime.utc_now()) == :gt)
          end)

        case active_payment_ban do
          nil ->
            {:error, :no_active_payment_ban}

          ban ->
            unban_user(ban.id, operator_id, unban_reason)
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取用户的支付封禁记录
  """
  def get_user_payment_bans(user_id) do
    case UserBan.list_by_user(user_id) do
      {:ok, bans} ->
        payment_bans = Enum.filter(bans, &(&1.ban_type == 4))
        {:ok, payment_bans}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # ==================== 私有函数 ====================

  defp validate_ban_operation(_user, _operator_id) do
    # 这里可以添加封禁权限验证逻辑
    :ok
  end

  defp update_ban_with_assets(_ban, _user) do
    # 记录用户被封禁时的资产状态
    :ok
  end

  defp notify_user_banned(_user, _ban) do
    # 通知用户被封禁
    :ok
  end

  defp notify_user_unbanned(_user_id, _ban) do
    # 通知用户解封
    :ok
  end
end
