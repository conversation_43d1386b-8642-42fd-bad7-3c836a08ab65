<div class="flex min-h-screen bg-base-200 font-sans relative">
  <!-- 移动端顶部栏 -->
  <div class="navbar bg-primary text-primary-content fixed top-0 left-0 right-0 z-[1001] lg:hidden shadow-lg">
    <div class="navbar-start">
      <button phx-click="toggle_sidebar" class="btn btn-ghost btn-circle">
        <i class="fas fa-bars text-xl"></i>
      </button>
    </div>
    <div class="navbar-center">
      <h1 class="text-lg font-semibold">{get_page_title(@current_page)}</h1>
    </div>
    <div class="navbar-end">
      <div class="avatar placeholder">
        <div class="bg-neutral-focus text-neutral-content rounded-full w-9">
          <span class="text-sm font-bold">
            {String.first(to_string(@current_user.username || "U"))}
          </span>
        </div>
      </div>
    </div>
  </div>
  
<!-- 侧边栏遮罩 -->
  <%= if @sidebar_open do %>
    <div class="fixed inset-0 bg-black bg-opacity-50 z-[999] lg:hidden" phx-click="toggle_sidebar">
    </div>
  <% end %>
  
<!-- 左侧导航栏 -->
  <div class={["sidebar", if(@sidebar_open, do: "sidebar-open", else: "")]}>
    <!-- 用户信息区域 -->
    <div class="user-info">
      <div class="user-avatar">
        {String.first(to_string(@current_user.username || "U"))}
      </div>
      <div class="user-details">
        <div class="username">{to_string(@current_user.username)}</div>
        <div class="user-role">
          {CypridinaWeb.AuthHelper.get_permission_level_name(@current_user)}
        </div>
      </div>
    </div>
    
<!-- 导航菜单 -->
    <nav class="nav-menu">
      <%= for menu_item <- get_navigation_menu(@current_user) do %>
        <%= if menu_item.type == :single do %>
          <!-- 单级菜单项 -->
          <.link
            navigate={~p"/admin_panel/#{menu_item.page}"}
            class={["nav-item", if(@current_page == menu_item.page, do: "active", else: "")]}
          >
            <i class={menu_item.icon}></i>
            <span>{menu_item.title}</span>
          </.link>
        <% else %>
          <!-- 分类菜单项 -->
          <div class="nav-category">
            <button
              phx-click="toggle_category"
              phx-value-category={menu_item.id}
              class={[
                "nav-category-header",
                if(category_expanded?(@expanded_categories, menu_item.id),
                  do: "expanded",
                  else: ""
                )
              ]}
            >
              <div class="nav-category-title">
                <i class={menu_item.icon}></i>
                <span>{menu_item.title}</span>
              </div>
              <i class="fas fa-chevron-down nav-category-arrow"></i>
            </button>

            <%= if category_expanded?(@expanded_categories, menu_item.id) do %>
              <div class="nav-category-children">
                <%= for child <- menu_item.children do %>
                  <.link
                    navigate={~p"/admin_panel/#{child.page}"}
                    class={[
                      "nav-child-item",
                      if(@current_page == child.page, do: "active", else: "")
                    ]}
                  >
                    <i class={child.icon}></i>
                    <span>{child.title}</span>
                    <%= if Map.get(child, :badge_count, 0) > 0 do %>
                      <span class="nav-badge">
                        {child.badge_count}
                      </span>
                    <% end %>
                  </.link>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </nav>
    
<!-- 底部操作 -->
    <div class="sidebar-footer">
      <a href="/" class="back-button">
        <i class="fas fa-arrow-left"></i>
        <span>返回游戏</span>
      </a>
    </div>
  </div>
  
<!-- 主内容区域 -->
  <div class="main-content">
    <!-- 页面标题栏 -->
    <div class="page-header">
      <div class="page-header-content">
        <div class="page-title-section">
          <h1 class="page-title">{get_page_title(@current_page)}</h1>
          <div class="page-breadcrumb">
            <span class="breadcrumb-item">管理面板</span>
            <i class="fas fa-chevron-right breadcrumb-separator"></i>
            <span class="breadcrumb-item current">{get_page_title(@current_page)}</span>
          </div>
        </div>
        <div class="page-actions">
          <div class="status-indicator">
            <div class="status-dot"></div>
            <span class="status-text">系统正常</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Flash 消息 -->
    <div class="flash-messages">
      <%= if live_flash(@flash, :info) do %>
        <div class="flash-message flash-success">
          <div class="flash-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="flash-content">
            <span class="flash-text">{live_flash(@flash, :info)}</span>
          </div>
          <button
            phx-click="lv:clear-flash"
            phx-value-key="info"
            class="flash-close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      <% end %>

      <%= if live_flash(@flash, :error) do %>
        <div class="flash-message flash-error">
          <div class="flash-icon">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="flash-content">
            <span class="flash-text">{live_flash(@flash, :error)}</span>
          </div>
          <button
            phx-click="lv:clear-flash"
            phx-value-key="error"
            class="flash-close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      <% end %>
    </div>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <div class="content-container">
        <%= case @current_page do %>
          <% "profile" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.ProfileComponent}
              id="profile-component"
              current_user={@current_user}
            />
          <% "users" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.UserManagementComponent}
              id="user-management-component"
              current_user={@current_user}
            />
          <% "subordinates" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.SubordinateManagementComponent}
              id="subordinate-management-component"
              current_user={@current_user}
            />
          <% "stocks" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.StockHoldingsComponent}
              id="stock-holdings-component"
              current_user={@current_user}
            />
          <% "bet_records" -> %>
            <.live_component
              module={RacingGame.Live.AdminPanel.BetRecordsComponent}
              id="bet-records-component"
              current_user={@current_user}
            />
          <% _ -> %>
            <div class="loading-container">
              <div class="loading-spinner">
                <div class="spinner"></div>
              </div>
              <span class="loading-text">页面正在加载中...</span>
            </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- 样式 CSS -->
<style>
  /* ===== 主内容区域样式 ===== */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding-top: 64px; /* 移动端顶部导航栏高度 */
    position: relative;
    overflow: hidden;
  }

  /* 背景装饰 */
  .main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  /* 页面标题栏 */
  .page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
  }

  .page-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .page-title-section {
    flex: 1;
  }

  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .page-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #718096;
  }

  .breadcrumb-item {
    color: #a0aec0;
    transition: color 0.2s ease;
  }

  .breadcrumb-item.current {
    color: #4a5568;
    font-weight: 500;
  }

  .breadcrumb-separator {
    font-size: 12px;
    color: #cbd5e0;
  }

  .page-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(72, 187, 120, 0.1);
    border: 1px solid rgba(72, 187, 120, 0.2);
    border-radius: 20px;
    font-size: 14px;
    color: #38a169;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    background: #48bb78;
    border-radius: 50%;
    animation: pulse-dot 2s infinite;
  }

  @keyframes pulse-dot {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }

  .status-text {
    font-weight: 500;
  }

  /* Flash 消息样式 */
  .flash-messages {
    padding: 0 32px;
    margin-top: 16px;
    position: relative;
    z-index: 10;
  }

  .flash-message {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 12px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: slideInDown 0.3s ease-out;
  }

  .flash-success {
    background: rgba(72, 187, 120, 0.1);
    border-color: rgba(72, 187, 120, 0.3);
    color: #2f855a;
  }

  .flash-error {
    background: rgba(245, 101, 101, 0.1);
    border-color: rgba(245, 101, 101, 0.3);
    color: #c53030;
  }

  .flash-icon {
    font-size: 20px;
    flex-shrink: 0;
  }

  .flash-content {
    flex: 1;
  }

  .flash-text {
    font-weight: 500;
    font-size: 15px;
  }

  .flash-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    opacity: 0.7;
  }

  .flash-close:hover {
    background: rgba(0, 0, 0, 0.1);
    opacity: 1;
  }

  @keyframes slideInDown {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 内容区域样式 */
  .content-wrapper {
    flex: 1;
    padding: 32px;
    position: relative;
    z-index: 5;
    overflow-y: auto;
    max-height: calc(100vh - 140px); /* 减去标题栏高度 */
  }

  .content-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    padding: 32px;
    min-height: 600px;
    position: relative;
    overflow: hidden;
  }

  /* 内容容器装饰 */
  .content-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.3), transparent);
  }

  .content-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #667eea, #764ba2);
    border-radius: 0 2px 2px 0;
  }

  /* 加载状态样式 */
  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 400px;
    gap: 24px;
  }

  .loading-spinner {
    position: relative;
  }

  .spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(120, 119, 198, 0.1);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: relative;
  }

  .spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 0.7;
      transform: translate(-50%, -50%) scale(0.8);
    }
  }

  .loading-text {
    font-size: 16px;
    color: #718096;
    font-weight: 500;
    animation: fadeInOut 2s ease-in-out infinite;
  }

  @keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
  }

  /* 左侧导航栏 */
  .sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: transform 0.3s ease;
  }

  /* 用户信息区域 */
  .user-info {
    padding: 30px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .user-details {
    flex: 1;
  }

  .username {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
  }

  .user-role {
    font-size: 14px;
    opacity: 0.8;
    color: #e0e7ff;
  }

  /* 导航菜单 */
  .nav-menu {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
  }

  /* 单级菜单项 */
  .nav-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 25px;
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    border-left: 4px solid transparent;
    text-decoration: none;
  }

  .nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.5);
  }

  .nav-item.active {
    background: rgba(255, 255, 255, 0.2);
    border-left-color: #ffd700;
    font-weight: 600;
  }

  .nav-item i {
    width: 20px;
    text-align: center;
    font-size: 18px;
  }

  /* 分类菜单 */
  .nav-category {
    margin-bottom: 8px;
  }

  .nav-category-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 25px;
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    border-left: 4px solid transparent;
  }

  .nav-category-header:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.3);
  }

  .nav-category-header.expanded {
    background: rgba(255, 255, 255, 0.15);
    border-left-color: rgba(255, 255, 255, 0.6);
  }

  .nav-category-title {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .nav-category-title i {
    width: 20px;
    text-align: center;
    font-size: 18px;
  }

  .nav-category-arrow {
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  .nav-category-header.expanded .nav-category-arrow {
    transform: rotate(180deg);
  }

  /* 子菜单项 */
  .nav-category-children {
    background: rgba(0, 0, 0, 0.1);
    border-left: 2px solid rgba(255, 255, 255, 0.2);
    margin-left: 25px;
    animation: slideDown 0.3s ease-out;
  }

  .nav-child-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 25px;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    border-left: 3px solid transparent;
    text-decoration: none;
  }

  .nav-child-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.4);
    color: white;
  }

  .nav-child-item.active {
    background: rgba(255, 255, 255, 0.2);
    border-left-color: #ffd700;
    font-weight: 600;
    color: white;
  }

  .nav-child-item i {
    width: 18px;
    text-align: center;
    font-size: 16px;
  }

  /* Badge 样式 */
  .nav-badge {
    background: #ef4444;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  /* 动画效果 */
  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      max-height: 500px;
      transform: translateY(0);
    }
  }

  /* 底部操作 */
  .sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
  }

  .back-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  /* 响应式设计 */
  @media (max-width: 1023px) {
    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      width: 280px;
      z-index: 1000;
      transform: translateX(-100%);
    }

    .sidebar-open {
      transform: translateX(0);
    }

    .user-info {
      padding: 20px 15px;
    }

    .nav-item, .nav-category-header {
      padding: 12px 20px;
      font-size: 15px;
    }

    .nav-child-item {
      padding: 10px 20px;
      font-size: 14px;
    }

    .nav-category-children {
      margin-left: 15px;
    }
  }

  /* 平板设备 */
  @media (max-width: 1024px) and (min-width: 769px) {
    .sidebar {
      width: 260px;
    }

    .nav-item, .nav-category-header {
      padding: 13px 22px;
    }

    .nav-child-item {
      padding: 11px 22px;
    }
  }

  /* 大屏幕优化 */
  @media (min-width: 1200px) {
    .sidebar {
      width: 300px;
    }

    .nav-item, .nav-category-header {
      padding: 16px 28px;
      font-size: 17px;
    }

    .nav-child-item {
      padding: 13px 28px;
      font-size: 16px;
    }
  }

  /* ===== 主内容区域响应式设计 ===== */
  /* 响应式设计 - 主内容区域 */
  @media (max-width: 1023px) {
    .main-content {
      padding-top: 64px; /* 移动端导航栏高度 */
    }

    .page-header {
      display: none; /* 移动端隐藏页面标题栏，使用顶部导航栏 */
    }

    .content-wrapper {
      padding: 16px;
      max-height: calc(100vh - 64px);
    }

    .content-container {
      padding: 20px;
      border-radius: 16px;
      min-height: 500px;
    }

    .flash-messages {
      padding: 0 16px;
      margin-top: 8px;
    }

    .flash-message {
      padding: 12px 16px;
      border-radius: 10px;
    }

    .page-title {
      font-size: 20px;
    }
  }

  /* 平板设备 */
  @media (max-width: 1024px) and (min-width: 769px) {
    .page-header-content {
      padding: 20px 24px;
    }

    .content-wrapper {
      padding: 24px;
    }

    .content-container {
      padding: 28px;
    }

    .page-title {
      font-size: 24px;
    }
  }

  /* 大屏幕优化 - 主内容区域 */
  @media (min-width: 1200px) {
    .page-header-content {
      padding: 28px 40px;
    }

    .content-wrapper {
      padding: 40px;
      max-height: calc(100vh - 160px);
    }

    .content-container {
      padding: 40px;
      border-radius: 24px;
    }

    .page-title {
      font-size: 32px;
    }
  }

  /* 桌面端隐藏移动端导航栏的影响 */
  @media (min-width: 1024px) {
    .main-content {
      padding-top: 0;
    }
  }

  /* 超大屏幕优化 */
  @media (min-width: 1440px) {
    .page-header-content {
      padding: 32px 48px;
    }

    .content-wrapper {
      padding: 48px;
    }

    .content-container {
      padding: 48px;
      border-radius: 28px;
    }
  }

  /* 高分辨率屏幕优化 */
  @media (min-resolution: 2dppx) {
    .content-container {
      box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
    }
  }

  /* 深色模式支持 */
  @media (prefers-color-scheme: dark) {
    .main-content {
      background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }

    .page-header {
      background: rgba(26, 32, 44, 0.95);
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .page-title {
      color: #e2e8f0;
    }

    .breadcrumb-item {
      color: #a0aec0;
    }

    .breadcrumb-item.current {
      color: #e2e8f0;
    }

    .content-container {
      background: rgba(26, 32, 44, 0.95);
      border-color: rgba(255, 255, 255, 0.1);
    }

    .flash-success {
      background: rgba(72, 187, 120, 0.2);
      color: #68d391;
    }

    .flash-error {
      background: rgba(245, 101, 101, 0.2);
      color: #fc8181;
    }

    .loading-text {
      color: #a0aec0;
    }
  }
</style>
