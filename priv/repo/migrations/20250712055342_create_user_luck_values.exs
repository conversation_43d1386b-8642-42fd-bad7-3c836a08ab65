defmodule Cypridina.Repo.Migrations.CreateUserLuckValues do
  use Ecto.Migration

  def change do
    create table(:user_luck_values, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :user_id, :integer, null: false
      add :game_type, :string, null: false, default: "teen_patti"
      add :current_luck, :integer, null: false, default: -1
      add :last_updated_at, :utc_datetime_usec

      timestamps()
    end

    create unique_index(:user_luck_values, [:user_id, :game_type])
    create index(:user_luck_values, [:current_luck])
    create index(:user_luck_values, [:game_type])
    create index(:user_luck_values, [:last_updated_at])
  end
end
