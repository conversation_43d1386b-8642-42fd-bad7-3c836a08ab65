{"mcpServers": {"Context7": {"type": "stdio", "command": "/usr/bin/context7-mcp", "args": [], "env": {}}, "tidewave": {"type": "sse", "url": "http://localhost:4000/tidewave/mcp"}, "Playwright": {"type": "stdio", "command": "/usr/bin/mcp-server-playwright", "args": [], "env": {}}, "ash_ai": {"command": "/app/cypridina/mcp-proxy", "args": ["http://localhost:4000/ash_ai/mcp"], "env": {}}, "zen": {"command": "sh", "args": ["-c", "exec $(which uvx || echo uvx) --from git+https://github.com/BeehiveInnovations/zen-mcp-server.git zen-mcp-server"], "env": {"PATH": "/usr/local/bin:/usr/bin:/bin:/opt/homebrew/bin:~/.local/bin"}}}}