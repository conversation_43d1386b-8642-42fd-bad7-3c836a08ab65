defmodule Teen.PaymentSystem.Reactors.CompleteRechargeReactorTest do
  @moduledoc """
  测试 CompleteRechargeReactor 工作流
  
  涵盖成功场景和各种失败场景，验证补偿机制
  """
  use Cypridina.DataCase

  alias Teen.PaymentSystem.RechargeRecord
  alias Teen.PaymentSystem.Reactors.CompleteRechargeReactor
  alias Cypridina.Accounts.User

  require Logger

  describe "CompleteRechargeReactor 成功场景" do
    test "完整的充值完成流程" do
      # 创建测试用户
      {:ok, user} = User.create(%{
        username: "test_user_#{System.unique_integer()}",
        password: "test_password"
      })

      # 创建充值记录
      {:ok, record} = RechargeRecord.create_recharge(%{
        user_id: user.id,
        amount: Decimal.new("100"),
        payment_method: "TEST_PAYMENT",
        currency: "XAA"
      })

      # 准备 Reactor 输入
      inputs = %{
        record_id: record.id,
        external_order_id: "TEST_EXT_#{System.unique_integer()}",
        callback_data: %{
          status: :success,
          payment_method: "TEST_PAYMENT",
          transaction_id: "TEST_TXN_123"
        }
      }

      # 执行 Reactor 工作流
      assert {:ok, result} = Reactor.run(CompleteRechargeReactor, inputs)

      # 验证结果
      assert result.status == :completed
      assert result.record.status == :completed
      assert result.record.external_order_id == inputs.external_order_id
      assert result.validation.validated == true
      assert result.event.event_published == true

      # 验证充值记录状态
      {:ok, updated_record} = RechargeRecord.get_by_id(record.id)
      assert updated_record.status == :completed
      assert updated_record.completed_at != nil
    end

    test "余额验证正确性" do
      {:ok, user} = User.create(%{
        username: "balance_test_#{System.unique_integer()}",
        password: "test_password"
      })

      # 获取初始余额
      user_identifier = Cypridina.Ledger.AccountIdentifier.user(user.id, :XAA)
      {:ok, balance_before} = Cypridina.Ledger.BalanceCache.get_balance(user_identifier)

      # 创建充值记录
      amount = Decimal.new("200")
      {:ok, record} = RechargeRecord.create_recharge(%{
        user_id: user.id,
        amount: amount,
        payment_method: "BALANCE_TEST",
        currency: "XAA"
      })

      # 执行充值完成流程
      inputs = %{
        record_id: record.id,
        external_order_id: "BALANCE_TEST_#{System.unique_integer()}",
        callback_data: %{status: :success}
      }

      assert {:ok, result} = Reactor.run(CompleteRechargeReactor, inputs)

      # 验证余额变化
      {:ok, balance_after} = Cypridina.Ledger.BalanceCache.get_balance(user_identifier)
      expected_increase = Decimal.to_integer(amount)
      
      assert balance_after == balance_before + expected_increase
      assert result.validation.balance_before == balance_before
      assert result.validation.balance_after == balance_after
    end
  end

  describe "CompleteRechargeReactor 失败场景" do
    test "无效的充值记录ID" do
      invalid_id = "00000000-0000-0000-0000-000000000000"
      
      inputs = %{
        record_id: invalid_id,
        external_order_id: "INVALID_TEST",
        callback_data: %{status: :success}
      }

      assert {:error, reason} = Reactor.run(CompleteRechargeReactor, inputs)
      assert reason =~ "Failed to find record"
    end

    test "Ledger 转账失败的补偿机制" do
      {:ok, user} = User.create(%{
        username: "ledger_fail_test_#{System.unique_integer()}",
        password: "test_password"
      })

      {:ok, record} = RechargeRecord.create_recharge(%{
        user_id: user.id,
        amount: Decimal.new("-1"),  # 无效金额，会导致 Ledger 失败
        payment_method: "FAIL_TEST",
        currency: "XAA"
      })

      inputs = %{
        record_id: record.id,
        external_order_id: "FAIL_TEST_#{System.unique_integer()}",
        callback_data: %{status: :success}
      }

      # 执行应该失败并触发补偿
      assert {:error, _reason} = Reactor.run(CompleteRechargeReactor, inputs)

      # 验证补偿机制：记录应该被标记为失败
      {:ok, compensated_record} = RechargeRecord.get_by_id(record.id)
      assert compensated_record.status == :failed
      assert compensated_record.error_message =~ "充值处理失败，已回滚"
    end

    test "余额验证失败的处理" do
      # 这个测试比较复杂，需要模拟金币添加成功但余额验证失败的情况
      # 在实际环境中，可以通过模拟 BalanceCache 返回错误值来测试
      
      {:ok, user} = User.create(%{
        username: "validation_fail_test_#{System.unique_integer()}",
        password: "test_password"
      })

      {:ok, record} = RechargeRecord.create_recharge(%{
        user_id: user.id,
        amount: Decimal.new("100"),
        payment_method: "VALIDATION_FAIL_TEST",
        currency: "XAA"
      })

      # 在这里可以添加模拟代码来模拟余额验证失败
      # 这需要依赖具体的测试框架和模拟库
      
      # 暂时跳过这个测试，在实际项目中需要实现
      # assert {:error, reason} = ...
      # assert reason =~ "Balance validation failed"
    end
  end

  describe "CompleteRechargeReactor 集成测试" do
    test "通过 RechargeRecord.complete_recharge_with_reactor 调用" do
      {:ok, user} = User.create(%{
        username: "integration_test_#{System.unique_integer()}",
        password: "test_password"
      })

      {:ok, record} = RechargeRecord.create_recharge(%{
        user_id: user.id,
        amount: Decimal.new("150"),
        payment_method: "INTEGRATION_TEST",
        currency: "XAA"
      })

      # 通过公共接口调用
      assert {:ok, result_record} = RechargeRecord.complete_recharge_with_reactor(
        record.id,
        "INT_EXT_#{System.unique_integer()}",
        %{
          status: :success,
          payment_method: "INTEGRATION_TEST"
        }
      )

      assert result_record.status == :completed
      assert result_record.external_order_id != nil
    end

    test "通过 complete_recharge_order 调用" do
      {:ok, user} = User.create(%{
        username: "order_test_#{System.unique_integer()}",
        password: "test_password"
      })

      {:ok, record} = RechargeRecord.create_recharge(%{
        user_id: user.id,
        amount: Decimal.new("75"),
        payment_method: "ORDER_TEST",
        currency: "XAA"
      })

      callback_result = %{
        status: :success,
        gateway_order_id: "GW_#{System.unique_integer()}",
        payment_method: "ORDER_TEST"
      }

      # 通过 complete_recharge_order 调用
      assert {:ok, result_record} = RechargeRecord.complete_recharge_order(
        record.order_id,
        callback_result
      )

      assert result_record.status == :completed
      assert result_record.external_order_id == callback_result.gateway_order_id
    end
  end

  describe "性能和并发测试" do
    @tag :performance
    test "并发处理多个充值请求" do
      # 创建多个用户和充值记录
      tasks = 1..5
      |> Enum.map(fn i ->
        Task.async(fn ->
          {:ok, user} = User.create(%{
            username: "concurrent_test_#{i}_#{System.unique_integer()}",
            password: "test_password"
          })

          {:ok, record} = RechargeRecord.create_recharge(%{
            user_id: user.id,
            amount: Decimal.new("#{i}0"),
            payment_method: "CONCURRENT_TEST",
            currency: "XAA"
          })

          # 执行充值完成
          RechargeRecord.complete_recharge_with_reactor(
            record.id,
            "CONC_EXT_#{i}_#{System.unique_integer()}",
            %{status: :success}
          )
        end)
      end)

      # 等待所有任务完成
      results = Task.await_many(tasks, 30_000)

      # 验证所有请求都成功
      Enum.each(results, fn result ->
        assert {:ok, record} = result
        assert record.status == :completed
      end)
    end
  end
end